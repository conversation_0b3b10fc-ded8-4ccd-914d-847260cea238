using System;
using System.ComponentModel.DataAnnotations;

namespace GroceryERP.Models
{
    public abstract class BaseEntity
    {
        [Key]
        public int Id { get; set; }
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public string? CreatedBy { get; set; }
        
        public DateTime? LastModifiedDate { get; set; }
        
        public string? LastModifiedBy { get; set; }
        
        public bool IsActive { get; set; } = true;
    }
}
