using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GroceryERP.Models
{
    public class SalesInvoice : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string SalesInvoiceNo { get; set; } = string.Empty;

        public DateTime Date { get; set; } = DateTime.Now;

        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        // Credit balance
        [Column(TypeName = "decimal(18,2)")]
        public decimal Balance { get; set; }

        // Cash balance given to customer when overpaid
        [Column(TypeName = "decimal(18,2)")]
        public decimal CashBalance { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalDiscount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Payment { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? CashlessAmount { get; set; }

        public int PaymentMethodId { get; set; }
        public virtual MetaData PaymentMethod { get; set; } = null!;

        [StringLength(100)]
        public string? CardOrVoucherNo { get; set; }

        public DateTime? DueDate { get; set; }

        public int StatusId { get; set; }
        public virtual MetaData Status { get; set; } = null!;

        [StringLength(200)]
        public string? CustomerName { get; set; }

        public int CustomerId { get; set; }
        public virtual Customer Customer { get; set; } = null!;

        [StringLength(10)]
        public string Counter { get; set; } = string.Empty;

        // Navigation properties
        public virtual ICollection<SalesInvoiceRecord> SalesInvoiceRecords { get; set; } = new List<SalesInvoiceRecord>();
    }
}
