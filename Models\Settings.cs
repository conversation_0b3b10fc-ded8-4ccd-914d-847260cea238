using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GroceryERP.Models
{
    public class Setting : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Value { get; set; }

        [StringLength(50)]
        public string DataType { get; set; } = "string"; // string, int, decimal, bool

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(50)]
        public string Category { get; set; } = "General";

        public bool IsSystem { get; set; } = false; // System settings cannot be deleted

        // Helper properties for common data types
        public bool BoolValue => bool.TryParse(Value, out var result) && result;
        public int IntValue => int.TryParse(Value, out var result) ? result : 0;
        public decimal DecimalValue => decimal.TryParse(Value, out var result) ? result : 0;
    }

    public class BusinessInfo : BaseEntity
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Address { get; set; }

        [StringLength(20)]
        public string? Telephone { get; set; }

        [StringLength(20)]
        public string? Mobile { get; set; }

        [StringLength(100)]
        public string? Email { get; set; }

        [StringLength(50)]
        public string? Website { get; set; }

        [StringLength(50)]
        public string? RegistrationNo { get; set; }

        [StringLength(50)]
        public string? TaxNo { get; set; }

        public byte[]? Logo { get; set; }

        [StringLength(500)]
        public string? Note { get; set; }
    }

    public class Sequence : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(10)]
        public string Prefix { get; set; } = string.Empty;

        public int Counter { get; set; } = 0;

        public int PadLength { get; set; } = 6;

        [StringLength(10)]
        public string? Suffix { get; set; }

        [StringLength(200)]
        public string? Description { get; set; }

        // Helper method to generate next number
        public string GetNextNumber()
        {
            Counter++;
            return $"{Prefix}{Counter.ToString().PadLeft(PadLength, '0')}{Suffix}";
        }
    }

    public class BarcodeSettings : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;

        [StringLength(10)]
        public string? Prefix { get; set; }

        public int Length { get; set; } = 13;

        public int EffectiveLength { get; set; } = 6;

        public bool IsWeightScale { get; set; } = false;

        [StringLength(500)]
        public string? Description { get; set; }
    }

    public class Cheque : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string ChequeNo { get; set; } = string.Empty;

        public DateTime Date { get; set; } = DateTime.Now;

        public DateTime DueDate { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required]
        [StringLength(200)]
        public string Bank { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Branch { get; set; }

        public int StatusId { get; set; }
        public virtual MetaData Status { get; set; } = null!; // Pending, Cleared, Bounced

        public int? CustomerId { get; set; }
        public virtual Customer? Customer { get; set; }

        public int? SupplierId { get; set; }
        public virtual Supplier? Supplier { get; set; }

        [StringLength(100)]
        public string? RefInvoiceNo { get; set; }

        [StringLength(500)]
        public string? Note { get; set; }

        [StringLength(50)]
        public string ProcessedBy { get; set; } = string.Empty;
    }

    public class BankAccount : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string AccountName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string AccountNo { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string BankName { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Branch { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Balance { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }
    }
}
