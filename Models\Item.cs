using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GroceryERP.Models
{
    public class Item : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string ItemCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string Barcode { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Quantity { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? RetailDiscount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? WholesaleDiscount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? SpecialDiscount { get; set; }

        [StringLength(50)]
        public string? SupplierCode { get; set; }

        [Required]
        [StringLength(200)]
        public string ItemName { get; set; } = string.Empty;

        [StringLength(200)]
        public string? ItemNameSinhala { get; set; }

        [StringLength(100)]
        public string? Model { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        public int? BrandId { get; set; }
        public virtual Brand? Brand { get; set; }

        public int? CategoryId { get; set; }
        public virtual Category? Category { get; set; }

        public int? SubCategoryId { get; set; }
        public virtual SubCategory? SubCategory { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? CaseQuantity { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? CasePrice { get; set; }

        public int? UomId { get; set; }
        public virtual UOM? UOM { get; set; }

        [StringLength(50)]
        public string? Rack { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? DeadStockLevel { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal SellingPrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? OldSellingPrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal ItemCost { get; set; }

        public bool ManageStock { get; set; } = true;

        public bool HasExpiryDate { get; set; } = false;

        public DateTime? ExpiryDate { get; set; }

        // Navigation properties
        public virtual ICollection<SalesInvoiceRecord> SalesInvoiceRecords { get; set; } = new List<SalesInvoiceRecord>();
        public virtual ICollection<PurchaseInvoiceRecord> PurchaseInvoiceRecords { get; set; } = new List<PurchaseInvoiceRecord>();
    }
}
