using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GroceryERP.Models
{
    public class Expense : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string ExpenseNo { get; set; } = string.Empty;

        public DateTime Date { get; set; } = DateTime.Now;

        [Required]
        [StringLength(200)]
        public string Description { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        public int ExpenseTypeId { get; set; }
        public virtual ExpenseType ExpenseType { get; set; } = null!;

        public int? SupplierId { get; set; }
        public virtual Supplier? Supplier { get; set; }

        [StringLength(100)]
        public string? Reference { get; set; }

        [StringLength(500)]
        public string? Note { get; set; }

        [StringLength(10)]
        public string Counter { get; set; } = string.Empty;
    }

    public class ExpenseType : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        // Navigation properties
        public virtual ICollection<Expense> Expenses { get; set; } = new List<Expense>();
    }
}
