using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GroceryERP.Models;

namespace GroceryERP.Services
{
    public interface IEmployeeService
    {
        Task<IEnumerable<Employee>> GetAllEmployeesAsync();
        Task<Employee?> GetEmployeeByIdAsync(int id);
        Task<Employee?> GetEmployeeByCodeAsync(string employeeCode);
        Task<IEnumerable<Employee>> SearchEmployeesByNameAsync(string name);
        Task<bool> CreateEmployeeAsync(Employee employee);
        Task<bool> UpdateEmployeeAsync(Employee employee);
        Task<bool> DeleteEmployeeAsync(int id);
        Task<IEnumerable<Designation>> GetAllDesignationsAsync();
        Task<bool> CreateDesignationAsync(Designation designation);
        Task<bool> UpdateDesignationAsync(Designation designation);
        Task<bool> DeleteDesignationAsync(int id);
    }
}
