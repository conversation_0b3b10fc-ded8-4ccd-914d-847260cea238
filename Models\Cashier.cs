using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GroceryERP.Models
{
    public class Cashier : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string CashierCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; }

        public DateTime? LastOpenDate { get; set; }

        public DateTime? LastCloseDate { get; set; }

        public bool IsOpen { get; set; } = false;

        [StringLength(10)]
        public string Counter { get; set; } = string.Empty;

        // Navigation properties
        public virtual ICollection<CashRecord> CashRecords { get; set; } = new List<CashRecord>();
        public virtual ICollection<CashierLog> CashierLogs { get; set; } = new List<CashierLog>();
        public virtual ICollection<CashierHistory> CashierHistories { get; set; } = new List<CashierHistory>();
    }

    public class CashRecord : BaseEntity
    {
        public DateTime Date { get; set; } = DateTime.Now;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required]
        [StringLength(50)]
        public string Type { get; set; } = string.Empty; // IN, OUT

        [StringLength(200)]
        public string? Description { get; set; }

        [StringLength(100)]
        public string? Reference { get; set; }

        public int CashierId { get; set; }
        public virtual Cashier Cashier { get; set; } = null!;

        [StringLength(10)]
        public string Counter { get; set; } = string.Empty;
    }

    public class CashierLog : BaseEntity
    {
        public DateTime Date { get; set; } = DateTime.Now;

        [Required]
        [StringLength(50)]
        public string Action { get; set; } = string.Empty; // OPEN, CLOSE

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [StringLength(500)]
        public string? Note { get; set; }

        public int CashierId { get; set; }
        public virtual Cashier Cashier { get; set; } = null!;

        [StringLength(50)]
        public string Username { get; set; } = string.Empty;
    }

    public class CashierHistory : BaseEntity
    {
        public DateTime Date { get; set; } = DateTime.Now;

        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal ClosingBalance { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalSales { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalExpenses { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal CashIn { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal CashOut { get; set; }

        public int CashierId { get; set; }
        public virtual Cashier Cashier { get; set; } = null!;

        [StringLength(50)]
        public string Username { get; set; } = string.Empty;
    }
}
