using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GroceryERP.Models
{
    public class Action : BaseEntity
    {
        public DateTime Date { get; set; } = DateTime.Now;

        public int TypeId { get; set; }
        public virtual MetaData Type { get; set; } = null!; // Stock Adjustment, Price Change, etc.

        [StringLength(100)]
        public string? Reference { get; set; } // Item code, invoice no, etc.

        [Required]
        [StringLength(10)]
        public string Operator { get; set; } = string.Empty; // +, -, =

        [StringLength(100)]
        public string? Change { get; set; } // Amount of change

        [StringLength(500)]
        public string? Remark { get; set; }

        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        [StringLength(10)]
        public string Counter { get; set; } = string.Empty;
    }

    public class DailySummary : BaseEntity
    {
        public DateTime Date { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalSales { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPurchases { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalExpenses { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalReturns { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal CashSales { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditSales { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal CardSales { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal ChequeSales { get; set; }

        public int TotalInvoices { get; set; }

        public int TotalCustomers { get; set; }

        [StringLength(10)]
        public string Counter { get; set; } = string.Empty;

        [StringLength(50)]
        public string GeneratedBy { get; set; } = string.Empty;
    }

    public class ItemSummaryRecord : BaseEntity
    {
        public DateTime Date { get; set; }

        public int ItemId { get; set; }
        public virtual Item Item { get; set; } = null!;

        [Column(TypeName = "decimal(18,3)")]
        public decimal QuantitySold { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalSales { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCost { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Profit { get; set; }

        [StringLength(10)]
        public string Counter { get; set; } = string.Empty;
    }

    public class Error : BaseEntity
    {
        public DateTime Date { get; set; } = DateTime.Now;

        [Required]
        [StringLength(100)]
        public string ErrorType { get; set; } = string.Empty;

        [Required]
        public string Message { get; set; } = string.Empty;

        public string? StackTrace { get; set; }

        [StringLength(50)]
        public string? Username { get; set; }

        [StringLength(100)]
        public string? Source { get; set; }

        public bool IsResolved { get; set; } = false;
    }
}
