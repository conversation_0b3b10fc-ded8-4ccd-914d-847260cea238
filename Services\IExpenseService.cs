using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GroceryERP.Models;

namespace GroceryERP.Services
{
    public interface IExpenseService
    {
        Task<IEnumerable<Expense>> GetAllExpensesAsync();
        Task<Expense?> GetExpenseByIdAsync(int id);
        Task<Expense?> GetExpenseByNumberAsync(string expenseNumber);
        Task<IEnumerable<Expense>> GetExpensesByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<string?> CreateExpenseAsync(Expense expense);
        Task<bool> UpdateExpenseAsync(Expense expense);
        Task<bool> DeleteExpenseAsync(int id);
        Task<decimal> GetTotalExpensesForDateAsync(DateTime date);
        Task<decimal> GetTotalExpensesForDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<ExpenseType>> GetAllExpenseTypesAsync();
        Task<bool> CreateExpenseTypeAsync(ExpenseType expenseType);
        Task<bool> UpdateExpenseTypeAsync(ExpenseType expenseType);
        Task<bool> DeleteExpenseTypeAsync(int id);
    }
}
