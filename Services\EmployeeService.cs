using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using GroceryERP.Data;
using GroceryERP.Models;

namespace GroceryERP.Services
{
    public class EmployeeService : IEmployeeService
    {
        private readonly GroceryERPContext _context;

        public EmployeeService(GroceryERPContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Employee>> GetAllEmployeesAsync()
        {
            return await _context.Employees
                .Include(e => e.Designation)
                .Where(e => e.IsActive)
                .OrderBy(e => e.FirstName)
                .ToListAsync();
        }

        public async Task<Employee?> GetEmployeeByIdAsync(int id)
        {
            return await _context.Employees
                .Include(e => e.Designation)
                .FirstOrDefaultAsync(e => e.Id == id && e.IsActive);
        }

        public async Task<Employee?> GetEmployeeByCodeAsync(string employeeCode)
        {
            return await _context.Employees
                .Include(e => e.Designation)
                .FirstOrDefaultAsync(e => e.EmployeeCode == employeeCode && e.IsActive);
        }

        public async Task<IEnumerable<Employee>> SearchEmployeesByNameAsync(string name)
        {
            return await _context.Employees
                .Include(e => e.Designation)
                .Where(e => e.IsActive && 
                           (e.FirstName.Contains(name) || e.LastName.Contains(name)))
                .OrderBy(e => e.FirstName)
                .Take(50)
                .ToListAsync();
        }

        public async Task<bool> CreateEmployeeAsync(Employee employee)
        {
            try
            {
                var existingEmployee = await _context.Employees
                    .FirstOrDefaultAsync(e => e.EmployeeCode == employee.EmployeeCode);

                if (existingEmployee != null)
                    return false;

                _context.Employees.Add(employee);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateEmployeeAsync(Employee employee)
        {
            try
            {
                var existingEmployee = await _context.Employees
                    .FirstOrDefaultAsync(e => e.Id != employee.Id && e.EmployeeCode == employee.EmployeeCode);

                if (existingEmployee != null)
                    return false;

                _context.Employees.Update(employee);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteEmployeeAsync(int id)
        {
            try
            {
                var employee = await _context.Employees.FindAsync(id);
                if (employee == null)
                    return false;

                employee.IsActive = false;
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<IEnumerable<Designation>> GetAllDesignationsAsync()
        {
            return await _context.Designations
                .Where(d => d.IsActive)
                .OrderBy(d => d.Name)
                .ToListAsync();
        }

        public async Task<bool> CreateDesignationAsync(Designation designation)
        {
            try
            {
                var existingDesignation = await _context.Designations
                    .FirstOrDefaultAsync(d => d.Name == designation.Name);

                if (existingDesignation != null)
                    return false;

                _context.Designations.Add(designation);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateDesignationAsync(Designation designation)
        {
            try
            {
                var existingDesignation = await _context.Designations
                    .FirstOrDefaultAsync(d => d.Id != designation.Id && d.Name == designation.Name);

                if (existingDesignation != null)
                    return false;

                _context.Designations.Update(designation);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteDesignationAsync(int id)
        {
            try
            {
                var designation = await _context.Designations.FindAsync(id);
                if (designation == null)
                    return false;

                var hasEmployees = await _context.Employees
                    .AnyAsync(e => e.DesignationId == id);

                if (hasEmployees)
                {
                    designation.IsActive = false;
                }
                else
                {
                    _context.Designations.Remove(designation);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
