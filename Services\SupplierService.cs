using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using GroceryERP.Data;
using GroceryERP.Models;

namespace GroceryERP.Services
{
    public class SupplierService : ISupplierService
    {
        private readonly GroceryERPContext _context;

        public SupplierService(GroceryERPContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Supplier>> GetAllSuppliersAsync()
        {
            return await _context.Suppliers
                .Where(s => s.IsActive)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<Supplier?> GetSupplierByIdAsync(int id)
        {
            return await _context.Suppliers
                .FirstOrDefaultAsync(s => s.Id == id && s.IsActive);
        }

        public async Task<Supplier?> GetSupplierByCodeAsync(string supplierCode)
        {
            return await _context.Suppliers
                .FirstOrDefaultAsync(s => s.SupplierCode == supplierCode && s.IsActive);
        }

        public async Task<IEnumerable<Supplier>> SearchSuppliersByNameAsync(string name)
        {
            return await _context.Suppliers
                .Where(s => s.IsActive && s.Name.Contains(name))
                .OrderBy(s => s.Name)
                .Take(50)
                .ToListAsync();
        }

        public async Task<bool> CreateSupplierAsync(Supplier supplier)
        {
            try
            {
                // Check if supplier code already exists
                var existingSupplier = await _context.Suppliers
                    .FirstOrDefaultAsync(s => s.SupplierCode == supplier.SupplierCode);

                if (existingSupplier != null)
                    return false;

                _context.Suppliers.Add(supplier);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateSupplierAsync(Supplier supplier)
        {
            try
            {
                // Check if another supplier has the same supplier code
                var existingSupplier = await _context.Suppliers
                    .FirstOrDefaultAsync(s => s.Id != supplier.Id && s.SupplierCode == supplier.SupplierCode);

                if (existingSupplier != null)
                    return false;

                _context.Suppliers.Update(supplier);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteSupplierAsync(int id)
        {
            try
            {
                var supplier = await _context.Suppliers.FindAsync(id);
                if (supplier == null)
                    return false;

                // Check if supplier has any purchase invoices
                var hasPurchaseInvoices = await _context.PurchaseInvoices
                    .AnyAsync(pi => pi.SupplierId == id);

                if (hasPurchaseInvoices)
                {
                    // Soft delete if has transactions
                    supplier.IsActive = false;
                }
                else
                {
                    // Hard delete if no transactions
                    _context.Suppliers.Remove(supplier);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
