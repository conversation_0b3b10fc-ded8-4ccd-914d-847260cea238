using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace GroceryERP.Models
{
    public class UOM : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;

        [StringLength(10)]
        public string? Symbol { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        // Navigation properties
        public virtual ICollection<Item> Items { get; set; } = new List<Item>();
    }
}
