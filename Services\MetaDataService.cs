using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using GroceryERP.Data;
using GroceryERP.Models;

namespace GroceryERP.Services
{
    public class MetaDataService : IMetaDataService
    {
        private readonly GroceryERPContext _context;

        public MetaDataService(GroceryERPContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<MetaData>> GetAllMetaDataAsync()
        {
            return await _context.MetaData
                .Where(md => md.IsActive)
                .OrderBy(md => md.Category)
                .ThenBy(md => md.SortOrder)
                .ThenBy(md => md.Value)
                .ToListAsync();
        }

        public async Task<IEnumerable<MetaData>> GetMetaDataByCategoryAsync(string category)
        {
            return await _context.MetaData
                .Where(md => md.IsActive && md.Category == category)
                .OrderBy(md => md.SortOrder)
                .ThenBy(md => md.Value)
                .ToListAsync();
        }

        public async Task<MetaData?> GetMetaDataByIdAsync(int id)
        {
            return await _context.MetaData
                .FirstOrDefaultAsync(md => md.Id == id && md.IsActive);
        }

        public async Task<MetaData?> SearchMetaDataAsync(string value, string category)
        {
            return await _context.MetaData
                .FirstOrDefaultAsync(md => md.IsActive && md.Value == value && md.Category == category);
        }

        public async Task<bool> CreateMetaDataAsync(MetaData metaData)
        {
            try
            {
                // Check if the same value exists in the same category
                var existing = await _context.MetaData
                    .FirstOrDefaultAsync(md => md.Value == metaData.Value && md.Category == metaData.Category);

                if (existing != null)
                    return false;

                _context.MetaData.Add(metaData);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateMetaDataAsync(MetaData metaData)
        {
            try
            {
                // Check if another metadata has the same value in the same category
                var existing = await _context.MetaData
                    .FirstOrDefaultAsync(md => md.Id != metaData.Id && 
                                             md.Value == metaData.Value && 
                                             md.Category == metaData.Category);

                if (existing != null)
                    return false;

                _context.MetaData.Update(metaData);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteMetaDataAsync(int id)
        {
            try
            {
                var metaData = await _context.MetaData.FindAsync(id);
                if (metaData == null)
                    return false;

                // Check if metadata is being used
                var isUsed = await IsMetaDataInUseAsync(id);
                if (isUsed)
                {
                    // Soft delete if in use
                    metaData.IsActive = false;
                }
                else
                {
                    // Hard delete if not in use
                    _context.MetaData.Remove(metaData);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> IsMetaDataInUseAsync(int metaDataId)
        {
            // Check if metadata is used in sales invoices
            var usedInSalesInvoices = await _context.SalesInvoices
                .AnyAsync(si => si.PaymentMethodId == metaDataId || si.StatusId == metaDataId);

            if (usedInSalesInvoices)
                return true;

            // Check if metadata is used in sales invoice records
            var usedInSalesInvoiceRecords = await _context.SalesInvoiceRecords
                .AnyAsync(sir => sir.SaleTypeId == metaDataId || sir.BusinessTypeId == metaDataId);

            if (usedInSalesInvoiceRecords)
                return true;

            // Add more checks for other entities as needed

            return false;
        }
    }
}
