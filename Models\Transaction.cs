using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GroceryERP.Models
{
    public class Transaction : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string TransactionNo { get; set; } = string.Empty;

        public DateTime Date { get; set; } = DateTime.Now;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        public int TypeId { get; set; }
        public virtual MetaData Type { get; set; } = null!; // Sale, Purchase, Expense, etc.

        [StringLength(100)]
        public string? RefNo { get; set; } // Reference to invoice number

        [StringLength(200)]
        public string? Description { get; set; }

        [StringLength(50)]
        public string Operator { get; set; } = string.Empty; // Username

        [StringLength(10)]
        public string Counter { get; set; } = string.Empty;

        public int? CustomerId { get; set; }
        public virtual Customer? Customer { get; set; }

        public int? SupplierId { get; set; }
        public virtual Supplier? Supplier { get; set; }
    }

    public class ReturnRecord : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string ReturnNo { get; set; } = string.Empty;

        public DateTime Date { get; set; } = DateTime.Now;

        [Required]
        [StringLength(50)]
        public string OriginalInvoiceNo { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Type { get; set; } = string.Empty; // CUSTOMER_RETURN, SUPPLIER_RETURN

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        [StringLength(500)]
        public string? Reason { get; set; }

        public int? CustomerId { get; set; }
        public virtual Customer? Customer { get; set; }

        public int? SupplierId { get; set; }
        public virtual Supplier? Supplier { get; set; }

        [StringLength(50)]
        public string ProcessedBy { get; set; } = string.Empty;

        [StringLength(10)]
        public string Counter { get; set; } = string.Empty;

        // Navigation properties
        public virtual ICollection<ReturnRecordItem> ReturnItems { get; set; } = new List<ReturnRecordItem>();
    }

    public class ReturnRecordItem : BaseEntity
    {
        public int ReturnRecordId { get; set; }
        public virtual ReturnRecord ReturnRecord { get; set; } = null!;

        public int ItemId { get; set; }
        public virtual Item Item { get; set; } = null!;

        [Column(TypeName = "decimal(18,3)")]
        public decimal Quantity { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        [StringLength(500)]
        public string? Reason { get; set; }
    }
}
