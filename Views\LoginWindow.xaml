<Window x:Class="GroceryERP.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Grocery ERP - Login"
        Height="500"
        Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="White">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Margin="20,20,20,10" Padding="20" Background="#F5F5F5" CornerRadius="5">
            <StackPanel HorizontalAlignment="Center">
                <!-- Replace emoji with text or icon to avoid Designer crash -->
                <TextBlock Text="Store" FontSize="32" FontWeight="Bold" HorizontalAlignment="Center"/>
                <TextBlock Text="Grocery ERP" FontSize="24" FontWeight="Bold"
                          HorizontalAlignment="Center" Margin="0,10,0,0"/>
                <TextBlock Text="Desktop Management System"
                          HorizontalAlignment="Center"
                          Foreground="Gray"/>
            </StackPanel>
        </Border>

        <!-- Login Form -->
        <Border Grid.Row="1" Margin="20,10,15,10" Padding="10" Background="#F9F9F9" CornerRadius="5">
            <StackPanel>
                <TextBlock Text="Sign In" FontSize="16" FontWeight="Medium"
                          HorizontalAlignment="Center" Margin="0,0,0,20"/>

                <Label Content="Username:" Margin="0,0,0,5"/>
                <TextBox x:Name="TxtUsername"
                        Margin="0,0,0,15"
                        Padding="8"
                        Text="admin"/>

                <Label Content="Password:" Margin="0,0,0,5"/>
                <PasswordBox x:Name="TxtPassword"
                            Margin="0,0,0,20"
                            Padding="8"/>

                <Button x:Name="BtnLogin"
                       Content="LOGIN"
                       Click="BtnLogin_Click"
                       IsDefault="True"
                       Width="150"
                       Height="35"
                       Background="#2196F3"
                       Foreground="White"
                       BorderThickness="0"
                       HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Footer -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="20">
            <TextBlock Text="Version 1.0.0"
                      Foreground="Gray"
                      FontSize="12"/>
            <TextBlock Text=" | "
                      Foreground="Gray"
                      FontSize="12"/>
            <TextBlock Text="© 2025 Grocery ERP"
                      Foreground="Gray"
                      FontSize="12"/>
        </StackPanel>
    </Grid>
</Window>
