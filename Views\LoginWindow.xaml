<Window x:Class="GroceryERP.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Viganana Desktop - Login"
        Height="500"
        Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <materialDesign:Card Margin="20" Padding="32">
        <StackPanel>
            <!-- Header -->
            <StackPanel HorizontalAlignment="Center" Margin="0,0,0,32">
                <materialDesign:PackIcon Kind="Store"
                                       Width="64"
                                       Height="64"
                                       HorizontalAlignment="Center"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                <TextBlock Text="Viganana Desktop"
                          Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                          HorizontalAlignment="Center"
                          Margin="0,16,0,0"/>
                <TextBlock Text="Grocery ERP Management System"
                          Style="{StaticResource MaterialDesignBody2TextBlock}"
                          HorizontalAlignment="Center"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>

            <!-- Login Form -->
            <StackPanel>
                <TextBox x:Name="TxtUsername"
                        materialDesign:HintAssist.Hint="Username"
                        Style="{StaticResource MaterialDesignFilledTextBox}"
                        Margin="0,0,0,16"
                        Text="admin"/>

                <PasswordBox x:Name="TxtPassword"
                            materialDesign:HintAssist.Hint="Password"
                            Style="{StaticResource MaterialDesignFilledPasswordBox}"
                            Margin="0,0,0,24"/>

                <Button x:Name="BtnLogin"
                       Content="LOGIN"
                       Click="BtnLogin_Click"
                       IsDefault="True"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Margin="0,0,0,16"
                       Height="40"/>

                <TextBlock x:Name="TxtMessage"
                          HorizontalAlignment="Center"
                          Foreground="{DynamicResource MaterialDesignValidationErrorBrush}"
                          Margin="0,8,0,0"
                          TextWrapping="Wrap"/>
            </StackPanel>

            <!-- Footer -->
            <TextBlock Text="© 2024 Viganana Desktop. All rights reserved."
                      HorizontalAlignment="Center"
                      Style="{StaticResource MaterialDesignCaptionTextBlock}"
                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                      Margin="0,24,0,0"/>
        </StackPanel>
    </materialDesign:Card>
</Window>
