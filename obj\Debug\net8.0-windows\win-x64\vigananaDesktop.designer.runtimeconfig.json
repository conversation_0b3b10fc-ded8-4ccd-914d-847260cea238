{"runtimeOptions": {"tfm": "net8.0", "frameworks": [{"name": "Microsoft.NETCore.App", "version": "8.0.17"}, {"name": "Microsoft.WindowsDesktop.App", "version": "8.0.17"}], "additionalProbingPaths": ["C:\\Users\\<USER>\\.dotnet\\store\\|arch|\\|tfm|", "C:\\Users\\<USER>\\.nuget\\packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configProperties": {"System.Reflection.NullabilityInfoContext.IsSupported": true, "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": true, "CSWINRT_USE_WINDOWS_UI_XAML_PROJECTIONS": false, "Microsoft.NETCore.DotNetHostPolicy.SetAppPaths": true}}}