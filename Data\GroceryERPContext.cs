using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using GroceryERP.Models;

namespace GroceryERP.Data
{
    public class GroceryERPContext : DbContext
    {
        public GroceryERPContext(DbContextOptions<GroceryERPContext> options) : base(options)
        {
        }

        // DbSets
        public DbSet<User> Users { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<Item> Items { get; set; }
        public DbSet<Brand> Brands { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<SubCategory> SubCategories { get; set; }
        public DbSet<UOM> UOMs { get; set; }
        public DbSet<MetaData> MetaData { get; set; }
        public DbSet<SalesInvoice> SalesInvoices { get; set; }
        public DbSet<SalesInvoiceRecord> SalesInvoiceRecords { get; set; }
        public DbSet<PurchaseInvoice> PurchaseInvoices { get; set; }
        public DbSet<PurchaseInvoiceRecord> PurchaseInvoiceRecords { get; set; }
        public DbSet<Employee> Employees { get; set; }
        public DbSet<Designation> Designations { get; set; }
        public DbSet<Expense> Expenses { get; set; }
        public DbSet<ExpenseType> ExpenseTypes { get; set; }
        public DbSet<Cashier> Cashiers { get; set; }
        public DbSet<CashRecord> CashRecords { get; set; }
        public DbSet<CashierLog> CashierLogs { get; set; }
        public DbSet<CashierHistory> CashierHistories { get; set; }
        public DbSet<Transaction> Transactions { get; set; }
        public DbSet<ReturnRecord> ReturnRecords { get; set; }
        public DbSet<ReturnRecordItem> ReturnRecordItems { get; set; }
        public DbSet<Setting> Settings { get; set; }
        public DbSet<BusinessInfo> BusinessInfos { get; set; }
        public DbSet<Sequence> Sequences { get; set; }
        public DbSet<BarcodeSettings> BarcodeSettings { get; set; }
        public DbSet<Cheque> Cheques { get; set; }
        public DbSet<BankAccount> BankAccounts { get; set; }
        public DbSet<Models.Action> Actions { get; set; }
        public DbSet<DailySummary> DailySummaries { get; set; }
        public DbSet<ItemSummaryRecord> ItemSummaryRecords { get; set; }
        public DbSet<Error> Errors { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure unique constraints
            modelBuilder.Entity<User>()
                .HasIndex(u => u.Username)
                .IsUnique();

            modelBuilder.Entity<Customer>()
                .HasIndex(c => c.CustomerCode)
                .IsUnique();

            modelBuilder.Entity<Supplier>()
                .HasIndex(s => s.SupplierCode)
                .IsUnique();

            modelBuilder.Entity<Item>()
                .HasIndex(i => i.ItemCode)
                .IsUnique();

            modelBuilder.Entity<Item>()
                .HasIndex(i => i.Barcode)
                .IsUnique();

            modelBuilder.Entity<SalesInvoice>()
                .HasIndex(si => si.SalesInvoiceNo)
                .IsUnique();

            modelBuilder.Entity<PurchaseInvoice>()
                .HasIndex(pi => pi.PurchaseInvoiceNo)
                .IsUnique();

            // Configure relationships
            ConfigureSalesInvoiceRelationships(modelBuilder);
            ConfigurePurchaseInvoiceRelationships(modelBuilder);
            ConfigureMetaDataRelationships(modelBuilder);
            ConfigureItemRelationships(modelBuilder);
            ConfigureUserRelationships(modelBuilder);
            ConfigureEmployeeRelationships(modelBuilder);
            ConfigureExpenseRelationships(modelBuilder);
            ConfigureCashierRelationships(modelBuilder);
            ConfigureTransactionRelationships(modelBuilder);
            ConfigureReturnRelationships(modelBuilder);
            ConfigureOtherRelationships(modelBuilder);

            // Seed initial data
            SeedInitialData(modelBuilder);
        }

        private void ConfigureSalesInvoiceRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<SalesInvoice>()
                .HasOne(si => si.Customer)
                .WithMany(c => c.SalesInvoices)
                .HasForeignKey(si => si.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SalesInvoice>()
                .HasOne(si => si.PaymentMethod)
                .WithMany(md => md.SalesInvoicesAsPaymentMethod)
                .HasForeignKey(si => si.PaymentMethodId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SalesInvoice>()
                .HasOne(si => si.Status)
                .WithMany(md => md.SalesInvoicesAsStatus)
                .HasForeignKey(si => si.StatusId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SalesInvoiceRecord>()
                .HasOne(sir => sir.SalesInvoice)
                .WithMany(si => si.SalesInvoiceRecords)
                .HasForeignKey(sir => sir.SalesInvoiceId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<SalesInvoiceRecord>()
                .HasOne(sir => sir.Item)
                .WithMany(i => i.SalesInvoiceRecords)
                .HasForeignKey(sir => sir.ItemId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SalesInvoiceRecord>()
                .HasOne(sir => sir.SaleType)
                .WithMany(md => md.SalesInvoiceRecordsAsSaleType)
                .HasForeignKey(sir => sir.SaleTypeId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SalesInvoiceRecord>()
                .HasOne(sir => sir.BusinessType)
                .WithMany(md => md.SalesInvoiceRecordsAsBusinessType)
                .HasForeignKey(sir => sir.BusinessTypeId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private void ConfigurePurchaseInvoiceRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<PurchaseInvoice>()
                .HasOne(pi => pi.Supplier)
                .WithMany(s => s.PurchaseInvoices)
                .HasForeignKey(pi => pi.SupplierId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<PurchaseInvoiceRecord>()
                .HasOne(pir => pir.PurchaseInvoice)
                .WithMany(pi => pi.PurchaseInvoiceRecords)
                .HasForeignKey(pir => pir.PurchaseInvoiceId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<PurchaseInvoiceRecord>()
                .HasOne(pir => pir.Item)
                .WithMany(i => i.PurchaseInvoiceRecords)
                .HasForeignKey(pir => pir.ItemId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private void ConfigureMetaDataRelationships(ModelBuilder modelBuilder)
        {
            // MetaData is used for multiple purposes, so we need to be careful with relationships
            // Most relationships are already configured in other methods
        }

        private void ConfigureItemRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Item>()
                .HasOne(i => i.Brand)
                .WithMany(b => b.Items)
                .HasForeignKey(i => i.BrandId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Item>()
                .HasOne(i => i.Category)
                .WithMany(c => c.Items)
                .HasForeignKey(i => i.CategoryId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Item>()
                .HasOne(i => i.SubCategory)
                .WithMany(sc => sc.Items)
                .HasForeignKey(i => i.SubCategoryId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Item>()
                .HasOne(i => i.UOM)
                .WithMany(u => u.Items)
                .HasForeignKey(i => i.UomId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<SubCategory>()
                .HasOne(sc => sc.Category)
                .WithMany(c => c.SubCategories)
                .HasForeignKey(sc => sc.CategoryId)
                .OnDelete(DeleteBehavior.Cascade);
        }

        private void ConfigureUserRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<UserRole>()
                .HasOne(ur => ur.User)
                .WithMany(u => u.UserRoles)
                .HasForeignKey(ur => ur.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Permission>()
                .HasOne(p => p.UserRole)
                .WithMany(ur => ur.Permissions)
                .HasForeignKey(p => p.UserRoleId)
                .OnDelete(DeleteBehavior.Cascade);
        }

        private void ConfigureEmployeeRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Employee>()
                .HasOne(e => e.Designation)
                .WithMany(d => d.Employees)
                .HasForeignKey(e => e.DesignationId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.EmployeeCode)
                .IsUnique();
        }

        private void ConfigureExpenseRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Expense>()
                .HasOne(e => e.ExpenseType)
                .WithMany(et => et.Expenses)
                .HasForeignKey(e => e.ExpenseTypeId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Expense>()
                .HasOne(e => e.Supplier)
                .WithMany()
                .HasForeignKey(e => e.SupplierId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Expense>()
                .HasIndex(e => e.ExpenseNo)
                .IsUnique();
        }

        private void ConfigureCashierRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<CashRecord>()
                .HasOne(cr => cr.Cashier)
                .WithMany(c => c.CashRecords)
                .HasForeignKey(cr => cr.CashierId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<CashierLog>()
                .HasOne(cl => cl.Cashier)
                .WithMany(c => c.CashierLogs)
                .HasForeignKey(cl => cl.CashierId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<CashierHistory>()
                .HasOne(ch => ch.Cashier)
                .WithMany(c => c.CashierHistories)
                .HasForeignKey(ch => ch.CashierId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Cashier>()
                .HasIndex(c => c.CashierCode)
                .IsUnique();
        }

        private void ConfigureTransactionRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Transaction>()
                .HasOne(t => t.Customer)
                .WithMany()
                .HasForeignKey(t => t.CustomerId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Transaction>()
                .HasOne(t => t.Supplier)
                .WithMany()
                .HasForeignKey(t => t.SupplierId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Transaction>()
                .HasIndex(t => t.TransactionNo)
                .IsUnique();
        }

        private void ConfigureReturnRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ReturnRecord>()
                .HasOne(rr => rr.Customer)
                .WithMany()
                .HasForeignKey(rr => rr.CustomerId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<ReturnRecord>()
                .HasOne(rr => rr.Supplier)
                .WithMany()
                .HasForeignKey(rr => rr.SupplierId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<ReturnRecordItem>()
                .HasOne(rri => rri.ReturnRecord)
                .WithMany(rr => rr.ReturnItems)
                .HasForeignKey(rri => rri.ReturnRecordId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<ReturnRecordItem>()
                .HasOne(rri => rri.Item)
                .WithMany()
                .HasForeignKey(rri => rri.ItemId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<ReturnRecord>()
                .HasIndex(rr => rr.ReturnNo)
                .IsUnique();
        }

        private void ConfigureOtherRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Cheque>()
                .HasOne(c => c.Customer)
                .WithMany()
                .HasForeignKey(c => c.CustomerId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Cheque>()
                .HasOne(c => c.Supplier)
                .WithMany()
                .HasForeignKey(c => c.SupplierId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<ItemSummaryRecord>()
                .HasOne(isr => isr.Item)
                .WithMany()
                .HasForeignKey(isr => isr.ItemId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Sequence>()
                .HasIndex(s => s.Name)
                .IsUnique();

            modelBuilder.Entity<Setting>()
                .HasIndex(s => s.Name)
                .IsUnique();
        }

        private void SeedInitialData(ModelBuilder modelBuilder)
        {
            // Seed MetaData for Payment Methods
            modelBuilder.Entity<MetaData>().HasData(
                new MetaData { Id = 1, Value = "Cash", Category = "PaymentMethod", SortOrder = 1, CreatedDate = DateTime.Now },
                new MetaData { Id = 2, Value = "Card", Category = "PaymentMethod", SortOrder = 2, CreatedDate = DateTime.Now },
                new MetaData { Id = 3, Value = "Cheque", Category = "PaymentMethod", SortOrder = 3, CreatedDate = DateTime.Now },
                new MetaData { Id = 4, Value = "Voucher", Category = "PaymentMethod", SortOrder = 4, CreatedDate = DateTime.Now }
            );

            // Seed MetaData for Payment Status
            modelBuilder.Entity<MetaData>().HasData(
                new MetaData { Id = 5, Value = "Pending", Category = "PaymentStatus", SortOrder = 1, CreatedDate = DateTime.Now },
                new MetaData { Id = 6, Value = "Partially paid", Category = "PaymentStatus", SortOrder = 2, CreatedDate = DateTime.Now },
                new MetaData { Id = 7, Value = "Completed", Category = "PaymentStatus", SortOrder = 3, CreatedDate = DateTime.Now }
            );

            // Seed MetaData for Sale Types
            modelBuilder.Entity<MetaData>().HasData(
                new MetaData { Id = 8, Value = "Sale", Category = "SaleType", SortOrder = 1, CreatedDate = DateTime.Now },
                new MetaData { Id = 9, Value = "Return", Category = "SaleType", SortOrder = 2, CreatedDate = DateTime.Now }
            );

            // Seed MetaData for Business Types
            modelBuilder.Entity<MetaData>().HasData(
                new MetaData { Id = 10, Value = "Retail", Category = "BusinessType", SortOrder = 1, CreatedDate = DateTime.Now },
                new MetaData { Id = 11, Value = "Wholesale", Category = "BusinessType", SortOrder = 2, CreatedDate = DateTime.Now },
                new MetaData { Id = 12, Value = "Special", Category = "BusinessType", SortOrder = 3, CreatedDate = DateTime.Now },
                new MetaData { Id = 13, Value = "Normal", Category = "BusinessType", SortOrder = 4, CreatedDate = DateTime.Now }
            );

            // Seed default customer
            modelBuilder.Entity<Customer>().HasData(
                new Customer 
                { 
                    Id = 1, 
                    CustomerCode = "DEFAULT", 
                    Name = "Default Customer", 
                    CreatedDate = DateTime.Now 
                }
            );

            // Seed default admin user
            modelBuilder.Entity<User>().HasData(
                new User 
                { 
                    Id = 1, 
                    Username = "admin", 
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"), 
                    FirstName = "System", 
                    LastName = "Administrator",
                    Email = "<EMAIL>",
                    CreatedDate = DateTime.Now 
                }
            );

            // Seed default UOMs
            modelBuilder.Entity<UOM>().HasData(
                new UOM { Id = 1, Name = "Piece", Symbol = "pcs", CreatedDate = DateTime.Now },
                new UOM { Id = 2, Name = "Kilogram", Symbol = "kg", CreatedDate = DateTime.Now },
                new UOM { Id = 3, Name = "Liter", Symbol = "l", CreatedDate = DateTime.Now },
                new UOM { Id = 4, Name = "Meter", Symbol = "m", CreatedDate = DateTime.Now }
            );

            // Seed default expense types
            modelBuilder.Entity<ExpenseType>().HasData(
                new ExpenseType { Id = 1, Name = "Office Supplies", Description = "General office supplies", CreatedDate = DateTime.Now },
                new ExpenseType { Id = 2, Name = "Utilities", Description = "Electricity, water, internet", CreatedDate = DateTime.Now },
                new ExpenseType { Id = 3, Name = "Transportation", Description = "Vehicle fuel, maintenance", CreatedDate = DateTime.Now },
                new ExpenseType { Id = 4, Name = "Marketing", Description = "Advertising and promotion", CreatedDate = DateTime.Now },
                new ExpenseType { Id = 5, Name = "Maintenance", Description = "Equipment and facility maintenance", CreatedDate = DateTime.Now }
            );

            // Seed default designations
            modelBuilder.Entity<Designation>().HasData(
                new Designation { Id = 1, Name = "Manager", Description = "Store Manager", BasicSalary = 50000, CreatedDate = DateTime.Now },
                new Designation { Id = 2, Name = "Cashier", Description = "Sales Cashier", BasicSalary = 30000, CreatedDate = DateTime.Now },
                new Designation { Id = 3, Name = "Stock Keeper", Description = "Inventory Management", BasicSalary = 25000, CreatedDate = DateTime.Now },
                new Designation { Id = 4, Name = "Sales Assistant", Description = "Sales Support", BasicSalary = 20000, CreatedDate = DateTime.Now }
            );

            // Seed default cashier
            modelBuilder.Entity<Cashier>().HasData(
                new Cashier
                {
                    Id = 1,
                    CashierCode = "CASH001",
                    Name = "Main Cashier",
                    OpeningBalance = 0,
                    CurrentBalance = 0,
                    Counter = "001",
                    CreatedDate = DateTime.Now
                }
            );

            // Seed default sequences
            modelBuilder.Entity<Sequence>().HasData(
                new Sequence { Id = 1, Name = "SalesInvoice", Prefix = "SI-", Counter = 0, PadLength = 6, Description = "Sales Invoice Numbers", CreatedDate = DateTime.Now },
                new Sequence { Id = 2, Name = "PurchaseInvoice", Prefix = "PI-", Counter = 0, PadLength = 6, Description = "Purchase Invoice Numbers", CreatedDate = DateTime.Now },
                new Sequence { Id = 3, Name = "Expense", Prefix = "EX-", Counter = 0, PadLength = 6, Description = "Expense Numbers", CreatedDate = DateTime.Now },
                new Sequence { Id = 4, Name = "Return", Prefix = "RT-", Counter = 0, PadLength = 6, Description = "Return Numbers", CreatedDate = DateTime.Now }
            );

            // Seed default settings
            modelBuilder.Entity<Setting>().HasData(
                new Setting { Id = 1, Name = "noStockModeSetting", Value = "false", DataType = "bool", Description = "Allow sales without stock", Category = "Sales", CreatedDate = DateTime.Now },
                new Setting { Id = 2, Name = "selfLearningSetting", Value = "true", DataType = "bool", Description = "Enable self learning mode", Category = "System", CreatedDate = DateTime.Now },
                new Setting { Id = 3, Name = "enableDayClose", Value = "true", DataType = "bool", Description = "Enable day close functionality", Category = "Cashier", CreatedDate = DateTime.Now },
                new Setting { Id = 4, Name = "defaultLanguage", Value = "English", DataType = "string", Description = "Default system language", Category = "System", CreatedDate = DateTime.Now },
                new Setting { Id = 5, Name = "prePriceIndicator", Value = "Rs.", DataType = "string", Description = "Price indicator prefix", Category = "Display", CreatedDate = DateTime.Now }
            );

            // Seed additional metadata for cheque status
            modelBuilder.Entity<MetaData>().HasData(
                new MetaData { Id = 14, Value = "Pending", Category = "ChequeStatus", SortOrder = 1, CreatedDate = DateTime.Now },
                new MetaData { Id = 15, Value = "Cleared", Category = "ChequeStatus", SortOrder = 2, CreatedDate = DateTime.Now },
                new MetaData { Id = 16, Value = "Bounced", Category = "ChequeStatus", SortOrder = 3, CreatedDate = DateTime.Now },
                new MetaData { Id = 17, Value = "Cancelled", Category = "ChequeStatus", SortOrder = 4, CreatedDate = DateTime.Now }
            );

            // Seed transaction types
            modelBuilder.Entity<MetaData>().HasData(
                new MetaData { Id = 18, Value = "Sale", Category = "TransactionType", SortOrder = 1, CreatedDate = DateTime.Now },
                new MetaData { Id = 19, Value = "Purchase", Category = "TransactionType", SortOrder = 2, CreatedDate = DateTime.Now },
                new MetaData { Id = 20, Value = "Expense", Category = "TransactionType", SortOrder = 3, CreatedDate = DateTime.Now },
                new MetaData { Id = 21, Value = "Return", Category = "TransactionType", SortOrder = 4, CreatedDate = DateTime.Now },
                new MetaData { Id = 22, Value = "Cash In", Category = "TransactionType", SortOrder = 5, CreatedDate = DateTime.Now },
                new MetaData { Id = 23, Value = "Cash Out", Category = "TransactionType", SortOrder = 6, CreatedDate = DateTime.Now }
            );

            // Seed action types
            modelBuilder.Entity<MetaData>().HasData(
                new MetaData { Id = 24, Value = "Stock Adjustment", Category = "ActionType", SortOrder = 1, CreatedDate = DateTime.Now },
                new MetaData { Id = 25, Value = "Price Change", Category = "ActionType", SortOrder = 2, CreatedDate = DateTime.Now },
                new MetaData { Id = 26, Value = "Item Created", Category = "ActionType", SortOrder = 3, CreatedDate = DateTime.Now },
                new MetaData { Id = 27, Value = "Item Updated", Category = "ActionType", SortOrder = 4, CreatedDate = DateTime.Now }
            );
        }

        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is BaseEntity && (e.State == EntityState.Added || e.State == EntityState.Modified));

            foreach (var entry in entries)
            {
                var entity = (BaseEntity)entry.Entity;

                if (entry.State == EntityState.Added)
                {
                    entity.CreatedDate = DateTime.Now;
                }
                else if (entry.State == EntityState.Modified)
                {
                    entity.LastModifiedDate = DateTime.Now;
                }
            }
        }
    }
}
