using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using GroceryERP.Data;
using GroceryERP.Models;

namespace GroceryERP.Services
{
    public class ItemService : IItemService
    {
        private readonly GroceryERPContext _context;

        public ItemService(GroceryERPContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Item>> GetAllItemsAsync()
        {
            return await _context.Items
                .Include(i => i.Brand)
                .Include(i => i.Category)
                .Include(i => i.SubCategory)
                .Include(i => i.UOM)
                .Where(i => i.IsActive)
                .OrderBy(i => i.ItemName)
                .ToListAsync();
        }

        public async Task<Item?> GetItemByIdAsync(int id)
        {
            return await _context.Items
                .Include(i => i.Brand)
                .Include(i => i.Category)
                .Include(i => i.SubCategory)
                .Include(i => i.UOM)
                .FirstOrDefaultAsync(i => i.Id == id && i.IsActive);
        }

        public async Task<Item?> GetItemByBarcodeAsync(string barcode)
        {
            return await _context.Items
                .Include(i => i.Brand)
                .Include(i => i.Category)
                .Include(i => i.SubCategory)
                .Include(i => i.UOM)
                .FirstOrDefaultAsync(i => i.Barcode == barcode && i.IsActive);
        }

        public async Task<Item?> GetItemByItemCodeAsync(string itemCode)
        {
            return await _context.Items
                .Include(i => i.Brand)
                .Include(i => i.Category)
                .Include(i => i.SubCategory)
                .Include(i => i.UOM)
                .FirstOrDefaultAsync(i => i.ItemCode == itemCode && i.IsActive);
        }

        public async Task<IEnumerable<Item>> SearchItemsByNameAsync(string name)
        {
            return await _context.Items
                .Include(i => i.Brand)
                .Include(i => i.Category)
                .Include(i => i.SubCategory)
                .Include(i => i.UOM)
                .Where(i => i.IsActive && 
                           (i.ItemName.Contains(name) || 
                            (i.ItemNameSinhala != null && i.ItemNameSinhala.Contains(name))))
                .OrderBy(i => i.ItemName)
                .Take(50)
                .ToListAsync();
        }

        public async Task<IEnumerable<Item>> SearchItemsByBarcodeAsync(string barcode)
        {
            return await _context.Items
                .Include(i => i.Brand)
                .Include(i => i.Category)
                .Include(i => i.SubCategory)
                .Include(i => i.UOM)
                .Where(i => i.IsActive && i.Barcode.Contains(barcode))
                .OrderBy(i => i.Barcode)
                .Take(50)
                .ToListAsync();
        }

        public async Task<bool> CreateItemAsync(Item item)
        {
            try
            {
                // Check if item code or barcode already exists
                var existingItem = await _context.Items
                    .FirstOrDefaultAsync(i => i.ItemCode == item.ItemCode || i.Barcode == item.Barcode);

                if (existingItem != null)
                    return false;

                _context.Items.Add(item);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateItemAsync(Item item)
        {
            try
            {
                // Check if another item has the same item code or barcode
                var existingItem = await _context.Items
                    .FirstOrDefaultAsync(i => i.Id != item.Id && 
                                            (i.ItemCode == item.ItemCode || i.Barcode == item.Barcode));

                if (existingItem != null)
                    return false;

                _context.Items.Update(item);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteItemAsync(int id)
        {
            try
            {
                var item = await _context.Items.FindAsync(id);
                if (item == null)
                    return false;

                // Soft delete
                item.IsActive = false;
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> AdjustStockAsync(string itemCode, decimal actualValue, string remark)
        {
            try
            {
                var item = await GetItemByItemCodeAsync(itemCode);
                if (item == null)
                    return false;

                var systemValue = item.Quantity;
                item.Quantity = actualValue;

                // TODO: Log the adjustment action
                // This would require an Action/Log entity similar to the Java version

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeductFromStockAsync(string itemCode, decimal quantity)
        {
            try
            {
                var item = await GetItemByItemCodeAsync(itemCode);
                if (item == null || !item.ManageStock)
                    return false;

                if (item.Quantity < quantity)
                    return false;

                item.Quantity -= quantity;
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> AddToStockAsync(string itemCode, decimal quantity)
        {
            try
            {
                var item = await GetItemByItemCodeAsync(itemCode);
                if (item == null)
                    return false;

                item.Quantity += quantity;
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> CheckStockAvailabilityAsync(string itemCode, decimal requiredQuantity)
        {
            var item = await GetItemByItemCodeAsync(itemCode);
            if (item == null || !item.ManageStock)
                return true; // If stock is not managed, consider it available

            return item.Quantity >= requiredQuantity;
        }
    }
}
