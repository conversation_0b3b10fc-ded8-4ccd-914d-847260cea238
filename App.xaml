<Application x:Class="GroceryERP.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Startup="Application_Startup">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Custom Styles -->
                <ResourceDictionary>
                    <Style x:Key="ModernButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                        <Setter Property="Margin" Value="5"/>
                        <Setter Property="Padding" Value="15,8"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                    </Style>
                    
                    <Style x:Key="ModernTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignTextBox}">
                        <Setter Property="Margin" Value="5"/>
                        <Setter Property="FontSize" Value="14"/>
                    </Style>
                    
                    <Style x:Key="ModernComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignComboBox}">
                        <Setter Property="Margin" Value="5"/>
                        <Setter Property="FontSize" Value="14"/>
                    </Style>
                    
                    <Style x:Key="ModernDataGrid" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
                        <Setter Property="AutoGenerateColumns" Value="False"/>
                        <Setter Property="CanUserAddRows" Value="False"/>
                        <Setter Property="CanUserDeleteRows" Value="False"/>
                        <Setter Property="IsReadOnly" Value="True"/>
                        <Setter Property="SelectionMode" Value="Single"/>
                        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
                        <Setter Property="HeadersVisibility" Value="Column"/>
                        <Setter Property="AlternatingRowBackground" Value="#F5F5F5"/>
                    </Style>
                    
                    <Style x:Key="TitleText" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="24"/>
                        <Setter Property="FontWeight" Value="Bold"/>
                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                        <Setter Property="Margin" Value="0,0,0,20"/>
                    </Style>
                    
                    <Style x:Key="SubtitleText" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="16"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                        <Setter Property="Margin" Value="0,0,0,10"/>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
