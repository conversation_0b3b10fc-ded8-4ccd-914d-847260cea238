using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GroceryERP.Models
{
    public class PurchaseInvoice : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string PurchaseInvoiceNo { get; set; } = string.Empty;

        [StringLength(50)]
        public string? InvoiceNo { get; set; } // Bill no given by vendor

        public DateTime Date { get; set; } = DateTime.Now;

        [StringLength(50)]
        public string? SupplierCode { get; set; }

        [StringLength(200)]
        public string? SupplierName { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Payment { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Balance { get; set; }

        public DateTime? DueDate { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Discount { get; set; }

        public int? PaymentMethodId { get; set; }
        public virtual MetaData? PaymentMethod { get; set; }

        public int? SupplierId { get; set; }
        public virtual Supplier? Supplier { get; set; }

        public int StatusId { get; set; }
        public virtual MetaData Status { get; set; } = null!;

        // Navigation properties
        public virtual ICollection<PurchaseInvoiceRecord> PurchaseInvoiceRecords { get; set; } = new List<PurchaseInvoiceRecord>();
    }

    public class PurchaseInvoiceRecord : BaseEntity
    {
        [Column(TypeName = "decimal(18,3)")]
        public decimal Quantity { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal ItemCost { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal SellingPrice { get; set; }

        [Required]
        [StringLength(50)]
        public string ItemCode { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Barcode { get; set; }

        [StringLength(50)]
        public string? PurchaseInvoiceNo { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Discount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; } // buying price * quantity

        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }

        public int ItemId { get; set; }
        public virtual Item Item { get; set; } = null!;

        public DateTime Date { get; set; } = DateTime.Now;

        public int? RecordTypeId { get; set; }
        public virtual MetaData? RecordType { get; set; }

        public int PurchaseInvoiceId { get; set; }
        public virtual PurchaseInvoice PurchaseInvoice { get; set; } = null!;
    }
}
