using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GroceryERP.Models;

namespace GroceryERP.Services
{
    public interface ISalesInvoiceService
    {
        Task<IEnumerable<SalesInvoice>> GetAllSalesInvoicesAsync();
        Task<SalesInvoice?> GetSalesInvoiceByIdAsync(int id);
        Task<SalesInvoice?> GetSalesInvoiceByNumberAsync(string invoiceNumber);
        Task<IEnumerable<SalesInvoice>> GetSalesInvoicesByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<SalesInvoice>> GetSalesInvoicesByCustomerAsync(int customerId);
        Task<string?> CreateSalesInvoiceAsync(SalesInvoice salesInvoice);
        Task<bool> UpdateSalesInvoiceAsync(SalesInvoice salesInvoice);
        Task<bool> DeleteSalesInvoiceAsync(int id);
        Task<decimal> GetTotalSalesForDateAsync(DateTime date);
        Task<decimal> GetTotalSalesForDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<SalesInvoice>> GetPendingPaymentsAsync();
    }
}
