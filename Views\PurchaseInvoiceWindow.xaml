<UserControl x:Class="GroceryERP.Views.PurchaseInvoiceWindow"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:sys="clr-namespace:System;assembly=mscorlib"
             Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,10" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="Purchase Invoice" Style="{StaticResource TitleText}" Margin="0"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="0,0,20,0">
                    <TextBlock Text="Date: " VerticalAlignment="Center"/>
                    <DatePicker x:Name="dpDate" SelectedDate="{x:Static sys:DateTime.Today}" 
                               Style="{StaticResource MaterialDesignDatePicker}" 
                               Width="120" Margin="5,0"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <TextBlock Text="Due Date: " VerticalAlignment="Center"/>
                    <DatePicker x:Name="dpDueDate" SelectedDate="{x:Static sys:DateTime.Today}" 
                               Style="{StaticResource MaterialDesignDatePicker}" 
                               Width="120" Margin="5,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Input Section -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,10" Padding="15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- First Row -->
                <Grid Grid.Row="0" Margin="0,0,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox x:Name="txtBarcode" 
                            Grid.Column="0"
                            materialDesign:HintAssist.Hint="Barcode / Item Code"
                            Style="{StaticResource ModernTextBox}"
                            KeyDown="TxtBarcode_KeyDown"/>

                    <TextBox x:Name="txtItemName" 
                            Grid.Column="1"
                            materialDesign:HintAssist.Hint="Item Name"
                            Style="{StaticResource ModernTextBox}"
                            IsReadOnly="True"/>

                    <TextBox x:Name="txtCost" 
                            Grid.Column="2"
                            materialDesign:HintAssist.Hint="Cost Price"
                            Style="{StaticResource ModernTextBox}"
                            KeyDown="TxtCost_KeyDown"/>

                    <TextBox x:Name="txtQuantity" 
                            Grid.Column="3"
                            materialDesign:HintAssist.Hint="Quantity"
                            Style="{StaticResource ModernTextBox}"
                            KeyDown="TxtQuantity_KeyDown"/>

                    <TextBox x:Name="txtSellingPrice" 
                            Grid.Column="4"
                            materialDesign:HintAssist.Hint="Selling Price"
                            Style="{StaticResource ModernTextBox}"/>

                    <Button x:Name="btnAdd" 
                           Grid.Column="5"
                           Content="ADD"
                           Style="{StaticResource ModernButton}"
                           Click="BtnAdd_Click"
                           Margin="10,5,0,5"/>
                </Grid>

                <!-- Second Row -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBox x:Name="txtSupplier" 
                            Grid.Column="0"
                            materialDesign:HintAssist.Hint="Supplier Name"
                            Style="{StaticResource ModernTextBox}"/>

                    <TextBox x:Name="txtInvoiceNo" 
                            Grid.Column="1"
                            materialDesign:HintAssist.Hint="Supplier Invoice No"
                            Style="{StaticResource ModernTextBox}"/>

                    <ComboBox x:Name="cmbPaymentMethod" 
                             Grid.Column="2"
                             materialDesign:HintAssist.Hint="Payment Method"
                             Style="{StaticResource ModernComboBox}"
                             DisplayMemberPath="Value"/>
                </Grid>
            </Grid>
        </materialDesign:Card>

        <!-- Items Grid -->
        <materialDesign:Card Grid.Row="2" Margin="0,0,0,10" Padding="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="Purchase Items" Style="{StaticResource SubtitleText}" Margin="0"/>
                    <Button x:Name="btnRemove" 
                           Content="REMOVE SELECTED"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Click="BtnRemove_Click"
                           Margin="20,0,0,0"/>
                </StackPanel>

                <DataGrid x:Name="dgItems" 
                         Grid.Row="1"
                         Style="{StaticResource ModernDataGrid}"
                         ItemsSource="{Binding InvoiceItems}">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="#" Binding="{Binding Index}" Width="50"/>
                        <DataGridTextColumn Header="Item Name" Binding="{Binding ItemName}" Width="*"/>
                        <DataGridTextColumn Header="Cost Price" Binding="{Binding ItemCost, StringFormat=C}" Width="100"/>
                        <DataGridTextColumn Header="Quantity" Binding="{Binding Quantity}" Width="80"/>
                        <DataGridTextColumn Header="Sub Total" Binding="{Binding SubTotal, StringFormat=C}" Width="100"/>
                        <DataGridTextColumn Header="Selling Price" Binding="{Binding SellingPrice, StringFormat=C}" Width="100"/>
                        <DataGridTextColumn Header="Total" Binding="{Binding TotalAmount, StringFormat=C}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- Totals and Actions -->
        <materialDesign:Card Grid.Row="3" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="300"/>
                    <ColumnDefinition Width="200"/>
                </Grid.ColumnDefinitions>

                <!-- Notes -->
                <StackPanel Grid.Column="0" VerticalAlignment="Center">
                    <TextBox x:Name="txtNotes" 
                            materialDesign:HintAssist.Hint="Notes"
                            Style="{StaticResource ModernTextBox}"
                            AcceptsReturn="True"
                            Height="80"
                            VerticalScrollBarVisibility="Auto"/>
                </StackPanel>

                <!-- Totals -->
                <Grid Grid.Column="1" Margin="20,0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Sub Total:" Margin="0,5"/>
                    <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtSubTotal" IsReadOnly="True" Margin="10,5,0,5"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Discount:" Margin="0,5"/>
                    <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtDiscount" Margin="10,5,0,5" Text="0"/>

                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Total:" Margin="0,5" FontWeight="Bold"/>
                    <TextBox Grid.Row="2" Grid.Column="1" x:Name="txtTotal" IsReadOnly="True" Margin="10,5,0,5" FontWeight="Bold"/>

                    <TextBlock Grid.Row="3" Grid.Column="0" Text="Payment:" Margin="0,5"/>
                    <TextBox Grid.Row="3" Grid.Column="1" x:Name="txtPayment" Margin="10,5,0,5" KeyDown="TxtPayment_KeyDown"/>

                    <TextBlock Grid.Row="4" Grid.Column="0" Text="Balance:" Margin="0,5"/>
                    <TextBox Grid.Row="4" Grid.Column="1" x:Name="txtBalance" IsReadOnly="True" Margin="10,5,0,5"/>
                </Grid>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="2" VerticalAlignment="Center">
                    <Button x:Name="btnSave" 
                           Content="SAVE"
                           Style="{StaticResource ModernButton}"
                           Click="BtnSave_Click"
                           Margin="0,5"/>

                    <Button x:Name="btnClear" 
                           Content="CLEAR"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Click="BtnClear_Click"
                           Margin="0,5"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
