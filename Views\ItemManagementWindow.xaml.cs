using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using GroceryERP.Models;
using GroceryERP.Services;

namespace GroceryERP.Views
{
    public partial class ItemManagementWindow : UserControl, INotifyPropertyChanged
    {
        private readonly IItemService _itemService;
        private readonly IMetaDataService _metaDataService;

        private ObservableCollection<Item> _items;
        private Item? _selectedItem;

        public ObservableCollection<Item> Items
        {
            get => _items;
            set
            {
                _items = value;
                OnPropertyChanged();
            }
        }

        public ItemManagementWindow(IItemService itemService, IMetaDataService metaDataService)
        {
            InitializeComponent();
            
            _itemService = itemService;
            _metaDataService = metaDataService;
            
            _items = new ObservableCollection<Item>();
            
            DataContext = this;
            
            InitializeAsync();
        }

        private async void InitializeAsync()
        {
            await LoadComboBoxData();
            await LoadItems();
            ClearForm();
        }

        private async Task LoadComboBoxData()
        {
            try
            {
                // Load categories, brands, and UOMs
                // For now, we'll create some sample data since we don't have these services yet
                // In a complete implementation, you would load these from their respective services
                
                var categories = new List<Category>
                {
                    new Category { Id = 1, Name = "Food & Beverages" },
                    new Category { Id = 2, Name = "Personal Care" },
                    new Category { Id = 3, Name = "Household" },
                    new Category { Id = 4, Name = "Electronics" }
                };
                cmbCategory.ItemsSource = categories;

                var brands = new List<Brand>
                {
                    new Brand { Id = 1, Name = "Generic" },
                    new Brand { Id = 2, Name = "Coca Cola" },
                    new Brand { Id = 3, Name = "Nestle" },
                    new Brand { Id = 4, Name = "Unilever" }
                };
                cmbBrand.ItemsSource = brands;

                var uoms = new List<UOM>
                {
                    new UOM { Id = 1, Name = "Piece", Symbol = "pcs" },
                    new UOM { Id = 2, Name = "Kilogram", Symbol = "kg" },
                    new UOM { Id = 3, Name = "Liter", Symbol = "l" },
                    new UOM { Id = 4, Name = "Meter", Symbol = "m" }
                };
                cmbUOM.ItemsSource = uoms;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading data: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadItems()
        {
            try
            {
                var items = await _itemService.GetAllItemsAsync();
                Items.Clear();
                foreach (var item in items)
                {
                    Items.Add(item);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading items: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void TxtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                _ = SearchItems();
            }
        }

        private async void BtnSearch_Click(object sender, RoutedEventArgs e)
        {
            await SearchItems();
        }

        private async Task SearchItems()
        {
            try
            {
                var searchText = txtSearch.Text.Trim();
                if (string.IsNullOrEmpty(searchText))
                {
                    await LoadItems();
                    return;
                }

                var items = await _itemService.SearchItemsByNameAsync(searchText);
                Items.Clear();
                foreach (var item in items)
                {
                    Items.Add(item);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error searching items: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DgItems_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (dgItems.SelectedItem is Item selectedItem)
            {
                _selectedItem = selectedItem;
                PopulateForm(selectedItem);
            }
        }

        private void PopulateForm(Item item)
        {
            txtItemCode.Text = item.ItemCode;
            txtBarcode.Text = item.Barcode;
            txtItemName.Text = item.ItemName;
            txtItemNameSinhala.Text = item.ItemNameSinhala ?? "";
            txtSupplierCode.Text = item.SupplierCode ?? "";
            txtCost.Text = item.ItemCost.ToString("F2");
            txtSellingPrice.Text = item.SellingPrice.ToString("F2");
            txtQuantity.Text = item.Quantity.ToString("F2");
            txtRetailDiscount.Text = (item.RetailDiscount ?? 0).ToString("F2");
            txtWholesaleDiscount.Text = (item.WholesaleDiscount ?? 0).ToString("F2");
            chkManageStock.IsChecked = item.ManageStock;

            // Set combo box selections
            if (item.CategoryId.HasValue)
            {
                cmbCategory.SelectedValue = item.CategoryId.Value;
            }
            if (item.BrandId.HasValue)
            {
                cmbBrand.SelectedValue = item.BrandId.Value;
            }
            if (item.UomId.HasValue)
            {
                cmbUOM.SelectedValue = item.UomId.Value;
            }
        }

        private void BtnNew_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
            _selectedItem = null;
            txtItemCode.Focus();
        }

        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            await SaveItem();
        }

        private async Task SaveItem()
        {
            try
            {
                if (!ValidateForm())
                    return;

                var item = new Item
                {
                    ItemCode = txtItemCode.Text.Trim(),
                    Barcode = txtBarcode.Text.Trim(),
                    ItemName = txtItemName.Text.Trim(),
                    ItemNameSinhala = txtItemNameSinhala.Text.Trim(),
                    SupplierCode = txtSupplierCode.Text.Trim(),
                    ItemCost = decimal.Parse(txtCost.Text),
                    SellingPrice = decimal.Parse(txtSellingPrice.Text),
                    Quantity = decimal.Parse(txtQuantity.Text),
                    RetailDiscount = string.IsNullOrEmpty(txtRetailDiscount.Text) ? null : decimal.Parse(txtRetailDiscount.Text),
                    WholesaleDiscount = string.IsNullOrEmpty(txtWholesaleDiscount.Text) ? null : decimal.Parse(txtWholesaleDiscount.Text),
                    ManageStock = chkManageStock.IsChecked ?? true,
                    CategoryId = (cmbCategory.SelectedItem as Category)?.Id,
                    BrandId = (cmbBrand.SelectedItem as Brand)?.Id,
                    UomId = (cmbUOM.SelectedItem as UOM)?.Id
                };

                var success = await _itemService.CreateItemAsync(item);
                
                if (success)
                {
                    MessageBox.Show("Item saved successfully!", "Success", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    await LoadItems();
                    ClearForm();
                }
                else
                {
                    MessageBox.Show("Failed to save item. Item code or barcode may already exist.", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving item: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnUpdate_Click(object sender, RoutedEventArgs e)
        {
            await UpdateItem();
        }

        private async Task UpdateItem()
        {
            try
            {
                if (_selectedItem == null)
                {
                    MessageBox.Show("Please select an item to update!", "Information", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                if (!ValidateForm())
                    return;

                _selectedItem.ItemCode = txtItemCode.Text.Trim();
                _selectedItem.Barcode = txtBarcode.Text.Trim();
                _selectedItem.ItemName = txtItemName.Text.Trim();
                _selectedItem.ItemNameSinhala = txtItemNameSinhala.Text.Trim();
                _selectedItem.SupplierCode = txtSupplierCode.Text.Trim();
                _selectedItem.ItemCost = decimal.Parse(txtCost.Text);
                _selectedItem.SellingPrice = decimal.Parse(txtSellingPrice.Text);
                _selectedItem.Quantity = decimal.Parse(txtQuantity.Text);
                _selectedItem.RetailDiscount = string.IsNullOrEmpty(txtRetailDiscount.Text) ? null : decimal.Parse(txtRetailDiscount.Text);
                _selectedItem.WholesaleDiscount = string.IsNullOrEmpty(txtWholesaleDiscount.Text) ? null : decimal.Parse(txtWholesaleDiscount.Text);
                _selectedItem.ManageStock = chkManageStock.IsChecked ?? true;
                _selectedItem.CategoryId = (cmbCategory.SelectedItem as Category)?.Id;
                _selectedItem.BrandId = (cmbBrand.SelectedItem as Brand)?.Id;
                _selectedItem.UomId = (cmbUOM.SelectedItem as UOM)?.Id;

                var success = await _itemService.UpdateItemAsync(_selectedItem);
                
                if (success)
                {
                    MessageBox.Show("Item updated successfully!", "Success", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    await LoadItems();
                    ClearForm();
                }
                else
                {
                    MessageBox.Show("Failed to update item. Item code or barcode may already exist.", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error updating item: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnDelete_Click(object sender, RoutedEventArgs e)
        {
            await DeleteItem();
        }

        private async Task DeleteItem()
        {
            try
            {
                if (_selectedItem == null)
                {
                    MessageBox.Show("Please select an item to delete!", "Information", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var result = MessageBox.Show($"Are you sure you want to delete item '{_selectedItem.ItemName}'?", 
                    "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    var success = await _itemService.DeleteItemAsync(_selectedItem.Id);
                    
                    if (success)
                    {
                        MessageBox.Show("Item deleted successfully!", "Success", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        await LoadItems();
                        ClearForm();
                    }
                    else
                    {
                        MessageBox.Show("Failed to delete item!", "Error", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error deleting item: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void ClearForm()
        {
            txtItemCode.Clear();
            txtBarcode.Clear();
            txtItemName.Clear();
            txtItemNameSinhala.Clear();
            txtSupplierCode.Clear();
            txtCost.Clear();
            txtSellingPrice.Clear();
            txtQuantity.Clear();
            txtRetailDiscount.Clear();
            txtWholesaleDiscount.Clear();
            chkManageStock.IsChecked = true;
            
            cmbCategory.SelectedIndex = -1;
            cmbBrand.SelectedIndex = -1;
            cmbUOM.SelectedIndex = -1;
            
            _selectedItem = null;
            dgItems.SelectedItem = null;
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrEmpty(txtItemCode.Text.Trim()))
            {
                MessageBox.Show("Please enter item code!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrEmpty(txtBarcode.Text.Trim()))
            {
                MessageBox.Show("Please enter barcode!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrEmpty(txtItemName.Text.Trim()))
            {
                MessageBox.Show("Please enter item name!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!decimal.TryParse(txtCost.Text, out _) || decimal.Parse(txtCost.Text) < 0)
            {
                MessageBox.Show("Please enter a valid cost price!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!decimal.TryParse(txtSellingPrice.Text, out _) || decimal.Parse(txtSellingPrice.Text) <= 0)
            {
                MessageBox.Show("Please enter a valid selling price!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!decimal.TryParse(txtQuantity.Text, out _) || decimal.Parse(txtQuantity.Text) < 0)
            {
                MessageBox.Show("Please enter a valid quantity!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
