using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GroceryERP.Models;

namespace GroceryERP.Services
{
    public interface IItemService
    {
        Task<IEnumerable<Item>> GetAllItemsAsync();
        Task<Item?> GetItemByIdAsync(int id);
        Task<Item?> GetItemByBarcodeAsync(string barcode);
        Task<Item?> GetItemByItemCodeAsync(string itemCode);
        Task<IEnumerable<Item>> SearchItemsByNameAsync(string name);
        Task<IEnumerable<Item>> SearchItemsByBarcodeAsync(string barcode);
        Task<bool> CreateItemAsync(Item item);
        Task<bool> UpdateItemAsync(Item item);
        Task<bool> DeleteItemAsync(int id);
        Task<bool> AdjustStockAsync(string itemCode, decimal actualValue, string remark);
        Task<bool> DeductFromStockAsync(string itemCode, decimal quantity);
        Task<bool> AddToStockAsync(string itemCode, decimal quantity);
        Task<bool> CheckStockAvailabilityAsync(string itemCode, decimal requiredQuantity);
    }
}
