using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GroceryERP.Models;

namespace GroceryERP.Services
{
    public interface IPurchaseInvoiceService
    {
        Task<IEnumerable<PurchaseInvoice>> GetAllPurchaseInvoicesAsync();
        Task<PurchaseInvoice?> GetPurchaseInvoiceByIdAsync(int id);
        Task<PurchaseInvoice?> GetPurchaseInvoiceByNumberAsync(string invoiceNumber);
        Task<IEnumerable<PurchaseInvoice>> GetPurchaseInvoicesByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<PurchaseInvoice>> GetPurchaseInvoicesBySupplierAsync(int supplierId);
        Task<string?> CreatePurchaseInvoiceAsync(PurchaseInvoice purchaseInvoice);
        Task<bool> UpdatePurchaseInvoiceAsync(PurchaseInvoice purchaseInvoice);
        Task<bool> DeletePurchaseInvoiceAsync(int id);
        Task<decimal> GetTotalPurchasesForDateAsync(DateTime date);
        Task<decimal> GetTotalPurchasesForDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<PurchaseInvoice>> GetPendingPaymentsAsync();
    }
}
