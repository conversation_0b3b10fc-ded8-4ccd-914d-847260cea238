using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace GroceryERP.Models
{
    public class MetaData : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Value { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Category { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        public int SortOrder { get; set; } = 0;

        // Navigation properties for different uses
        public virtual ICollection<SalesInvoice> SalesInvoicesAsPaymentMethod { get; set; } = new List<SalesInvoice>();
        public virtual ICollection<SalesInvoice> SalesInvoicesAsStatus { get; set; } = new List<SalesInvoice>();
        public virtual ICollection<SalesInvoiceRecord> SalesInvoiceRecordsAsSaleType { get; set; } = new List<SalesInvoiceRecord>();
        public virtual ICollection<SalesInvoiceRecord> SalesInvoiceRecordsAsBusinessType { get; set; } = new List<SalesInvoiceRecord>();
    }
}
