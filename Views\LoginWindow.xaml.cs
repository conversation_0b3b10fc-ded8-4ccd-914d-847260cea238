using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using GroceryERP.Data;

namespace GroceryERP.Views
{
    public partial class LoginWindow : Window
    {
        private readonly GroceryERPContext _context;

        public LoginWindow(GroceryERPContext context)
        {
            InitializeComponent();
            _context = context;
            
            // Set focus to username textbox
            Loaded += (s, e) => TxtUsername.Focus();
        }

        private async void BtnLogin_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await LoginAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Login error: {ex.Message}\n\nDetails: {ex}", "Login Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoginAsync()
        {
            try
            {
                BtnLogin.IsEnabled = false;
                BtnLogin.Content = "Logging in...";

                var username = TxtUsername.Text.Trim();
                var password = TxtPassword.Password;

                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    ShowError("Please enter both username and password.");
                    return;
                }

                // Find user
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == username && u.IsActive);

                if (user == null)
                {
                    ShowError("Invalid username or password.");
                    return;
                }

                // Verify password
                if (!BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
                {
                    ShowError("Invalid username or password.");
                    return;
                }

                // Update last login date
                user.LastLoginDate = DateTime.Now;
                await _context.SaveChangesAsync();

                // Store current user in application
                Application.Current.Properties["CurrentUser"] = user;

                // Open main window
                var mainWindow = App.GetService<MainWindow>();
                mainWindow.Show();

                // Close login window
                this.Close();
            }
            catch (Exception ex)
            {
                ShowError($"Login failed: {ex.Message}");
            }
            finally
            {
                BtnLogin.IsEnabled = true;
                BtnLogin.Content = "LOGIN";
            }
        }

        private void ShowError(string message)
        {
            TxtMessage.Text = message;
        }

        protected override void OnKeyDown(System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Enter)
            {
                _ = LoginAsync();
            }
            base.OnKeyDown(e);
        }
    }
}
