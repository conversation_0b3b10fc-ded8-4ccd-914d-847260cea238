using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.EntityFrameworkCore;
using GroceryERP.Data;
using GroceryERP.Models;
using GroceryERP.Services;
using GroceryERP.Views;

namespace GroceryERP
{
    public partial class App : Application
    {
        private IHost? _host;

        private async void Application_Startup(object sender, StartupEventArgs e)
        {
            // Create host
            _host = CreateHostBuilder().Build();

            // Ensure database is created and migrated
            await EnsureDatabaseAsync();

            // Start the host
            await _host.StartAsync();

            // Show login window
            var loginWindow = _host.Services.GetRequiredService<LoginWindow>();
            loginWindow.Show();
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }

            base.OnExit(e);
        }

        private IHostBuilder CreateHostBuilder()
        {
            return Host.CreateDefaultBuilder()
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.SetBasePath(Directory.GetCurrentDirectory());
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                })
                .ConfigureServices((context, services) =>
                {
                    // Configuration
                    services.AddSingleton(context.Configuration);

                    // Database
                    var connectionString = context.Configuration.GetConnectionString("DefaultConnection");

                    services.AddDbContext<GroceryERPContext>(options =>
                        options.UseSqlite(connectionString));

                    // Services
                    services.AddScoped<IItemService, ItemService>();
                    services.AddScoped<ICustomerService, CustomerService>();
                    services.AddScoped<ISupplierService, SupplierService>();
                    services.AddScoped<ISalesInvoiceService, SalesInvoiceService>();
                    services.AddScoped<IPurchaseInvoiceService, PurchaseInvoiceService>();
                    services.AddScoped<IEmployeeService, EmployeeService>();
                    services.AddScoped<IExpenseService, ExpenseService>();
                    services.AddScoped<IMetaDataService, MetaDataService>();

                    // Windows
                    services.AddTransient<LoginWindow>();
                    services.AddTransient<MainWindow>();
                    services.AddTransient<SalesInvoiceWindow>();
                    services.AddTransient<PurchaseInvoiceWindow>();
                    services.AddTransient<ItemManagementWindow>();

                    // ViewModels (if using MVVM pattern)
                    // services.AddTransient<MainViewModel>();
                    // services.AddTransient<SalesInvoiceViewModel>();
                });
        }

        private async Task EnsureDatabaseAsync()
        {
            using var scope = _host!.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<GroceryERPContext>();

            try
            {
                // Ensure database is created
                await context.Database.EnsureCreatedAsync();

                // Apply any pending migrations
                if ((await context.Database.GetPendingMigrationsAsync()).Any())
                {
                    await context.Database.MigrateAsync();
                }

                // Ensure admin user exists (fallback if seeding didn't work)
                await EnsureAdminUserAsync(context);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Database initialization failed: {ex.Message}", "Database Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        private async Task EnsureAdminUserAsync(GroceryERPContext context)
        {
            // Check if admin user exists
            var adminUser = await context.Users.FirstOrDefaultAsync(u => u.Username == "admin");

            if (adminUser == null)
            {
                // Create admin user
                adminUser = new Models.User
                {
                    Username = "admin",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                    FirstName = "System",
                    LastName = "Administrator",
                    Email = "<EMAIL>",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };

                context.Users.Add(adminUser);
                await context.SaveChangesAsync();
            }
        }

        public static T GetService<T>() where T : class
        {
            if (Current is App app && app._host != null)
            {
                return app._host.Services.GetRequiredService<T>();
            }
            throw new InvalidOperationException("Host is not available");
        }
    }
}
