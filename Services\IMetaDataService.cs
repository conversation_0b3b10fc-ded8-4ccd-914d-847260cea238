using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GroceryERP.Models;

namespace GroceryERP.Services
{
    public interface IMetaDataService
    {
        Task<IEnumerable<MetaData>> GetAllMetaDataAsync();
        Task<IEnumerable<MetaData>> GetMetaDataByCategoryAsync(string category);
        Task<MetaData?> GetMetaDataByIdAsync(int id);
        Task<MetaData?> SearchMetaDataAsync(string value, string category);
        Task<bool> CreateMetaDataAsync(MetaData metaData);
        Task<bool> UpdateMetaDataAsync(MetaData metaData);
        Task<bool> DeleteMetaDataAsync(int id);
    }
}
