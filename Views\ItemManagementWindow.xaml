<UserControl x:Class="GroceryERP.Views.ItemManagementWindow"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,10" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="Item Management" Style="{StaticResource TitleText}" Margin="0"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBox x:Name="txtSearch" 
                            materialDesign:HintAssist.Hint="Search items..."
                            Style="{StaticResource ModernTextBox}"
                            Width="200"
                            KeyDown="TxtSearch_KeyDown"/>
                    <Button x:Name="btnSearch" 
                           Content="SEARCH"
                           Style="{StaticResource ModernButton}"
                           Click="BtnSearch_Click"
                           Margin="10,0,0,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Item Form -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,10" Padding="15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- First Row -->
                <Grid Grid.Row="0" Margin="0,0,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBox x:Name="txtItemCode" 
                            Grid.Column="0"
                            materialDesign:HintAssist.Hint="Item Code"
                            Style="{StaticResource ModernTextBox}"/>

                    <TextBox x:Name="txtBarcode" 
                            Grid.Column="1"
                            materialDesign:HintAssist.Hint="Barcode"
                            Style="{StaticResource ModernTextBox}"/>

                    <TextBox x:Name="txtItemName" 
                            Grid.Column="2"
                            materialDesign:HintAssist.Hint="Item Name"
                            Style="{StaticResource ModernTextBox}"/>

                    <TextBox x:Name="txtItemNameSinhala" 
                            Grid.Column="3"
                            materialDesign:HintAssist.Hint="Item Name (Sinhala)"
                            Style="{StaticResource ModernTextBox}"/>
                </Grid>

                <!-- Second Row -->
                <Grid Grid.Row="1" Margin="0,0,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <ComboBox x:Name="cmbCategory" 
                             Grid.Column="0"
                             materialDesign:HintAssist.Hint="Category"
                             Style="{StaticResource ModernComboBox}"
                             DisplayMemberPath="Name"/>

                    <ComboBox x:Name="cmbBrand" 
                             Grid.Column="1"
                             materialDesign:HintAssist.Hint="Brand"
                             Style="{StaticResource ModernComboBox}"
                             DisplayMemberPath="Name"/>

                    <ComboBox x:Name="cmbUOM" 
                             Grid.Column="2"
                             materialDesign:HintAssist.Hint="Unit of Measure"
                             Style="{StaticResource ModernComboBox}"
                             DisplayMemberPath="Name"/>

                    <TextBox x:Name="txtSupplierCode" 
                            Grid.Column="3"
                            materialDesign:HintAssist.Hint="Supplier Code"
                            Style="{StaticResource ModernTextBox}"/>
                </Grid>

                <!-- Third Row -->
                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBox x:Name="txtCost" 
                            Grid.Column="0"
                            materialDesign:HintAssist.Hint="Cost Price"
                            Style="{StaticResource ModernTextBox}"/>

                    <TextBox x:Name="txtSellingPrice" 
                            Grid.Column="1"
                            materialDesign:HintAssist.Hint="Selling Price"
                            Style="{StaticResource ModernTextBox}"/>

                    <TextBox x:Name="txtQuantity" 
                            Grid.Column="2"
                            materialDesign:HintAssist.Hint="Quantity"
                            Style="{StaticResource ModernTextBox}"/>

                    <TextBox x:Name="txtRetailDiscount" 
                            Grid.Column="3"
                            materialDesign:HintAssist.Hint="Retail Discount"
                            Style="{StaticResource ModernTextBox}"/>

                    <TextBox x:Name="txtWholesaleDiscount" 
                            Grid.Column="4"
                            materialDesign:HintAssist.Hint="Wholesale Discount"
                            Style="{StaticResource ModernTextBox}"/>

                    <CheckBox x:Name="chkManageStock" 
                             Grid.Column="5"
                             Content="Manage Stock"
                             VerticalAlignment="Center"
                             IsChecked="True"
                             Margin="10,0,0,0"/>
                </Grid>
            </Grid>
        </materialDesign:Card>

        <!-- Items Grid -->
        <materialDesign:Card Grid.Row="2" Margin="0,0,0,10" Padding="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="Items List" Style="{StaticResource SubtitleText}" Margin="0,0,0,10"/>

                <DataGrid x:Name="dgItems" 
                         Grid.Row="1"
                         Style="{StaticResource ModernDataGrid}"
                         ItemsSource="{Binding Items}"
                         SelectionChanged="DgItems_SelectionChanged">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Item Code" Binding="{Binding ItemCode}" Width="100"/>
                        <DataGridTextColumn Header="Barcode" Binding="{Binding Barcode}" Width="120"/>
                        <DataGridTextColumn Header="Item Name" Binding="{Binding ItemName}" Width="*"/>
                        <DataGridTextColumn Header="Category" Binding="{Binding Category.Name}" Width="100"/>
                        <DataGridTextColumn Header="Brand" Binding="{Binding Brand.Name}" Width="100"/>
                        <DataGridTextColumn Header="Cost" Binding="{Binding ItemCost, StringFormat=C}" Width="80"/>
                        <DataGridTextColumn Header="Price" Binding="{Binding SellingPrice, StringFormat=C}" Width="80"/>
                        <DataGridTextColumn Header="Quantity" Binding="{Binding Quantity}" Width="80"/>
                        <DataGridCheckBoxColumn Header="Stock" Binding="{Binding ManageStock}" Width="60"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- Action Buttons -->
        <materialDesign:Card Grid.Row="3" Padding="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="btnNew" 
                       Content="NEW"
                       Style="{StaticResource ModernButton}"
                       Click="BtnNew_Click"
                       Margin="5"/>

                <Button x:Name="btnSave" 
                       Content="SAVE"
                       Style="{StaticResource ModernButton}"
                       Click="BtnSave_Click"
                       Margin="5"/>

                <Button x:Name="btnUpdate" 
                       Content="UPDATE"
                       Style="{StaticResource ModernButton}"
                       Click="BtnUpdate_Click"
                       Margin="5"/>

                <Button x:Name="btnDelete" 
                       Content="DELETE"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Click="BtnDelete_Click"
                       Margin="5"/>

                <Button x:Name="btnClear" 
                       Content="CLEAR"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Click="BtnClear_Click"
                       Margin="5"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
