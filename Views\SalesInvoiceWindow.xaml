<UserControl x:Class="GroceryERP.Views.SalesInvoiceWindow"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:sys="clr-namespace:System;assembly=mscorlib"
             Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,10" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="Sales Invoice" Style="{StaticResource TitleText}" Margin="0"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="Date: " VerticalAlignment="Center"/>
                    <DatePicker x:Name="dpDate" SelectedDate="{x:Static sys:DateTime.Today}" 
                               Style="{StaticResource MaterialDesignDatePicker}" 
                               Width="120" Margin="5,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Input Section -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,10" Padding="15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- First Row -->
                <Grid Grid.Row="0" Margin="0,0,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox x:Name="txtBarcode" 
                            Grid.Column="0"
                            materialDesign:HintAssist.Hint="Barcode / Item Code"
                            Style="{StaticResource ModernTextBox}"
                            KeyDown="TxtBarcode_KeyDown"/>

                    <TextBox x:Name="txtItemName" 
                            Grid.Column="1"
                            materialDesign:HintAssist.Hint="Item Name"
                            Style="{StaticResource ModernTextBox}"
                            IsReadOnly="True"/>

                    <TextBox x:Name="txtPrice" 
                            Grid.Column="2"
                            materialDesign:HintAssist.Hint="Price"
                            Style="{StaticResource ModernTextBox}"
                            KeyDown="TxtPrice_KeyDown"/>

                    <TextBox x:Name="txtQuantity" 
                            Grid.Column="3"
                            materialDesign:HintAssist.Hint="Quantity"
                            Style="{StaticResource ModernTextBox}"
                            KeyDown="TxtQuantity_KeyDown"/>

                    <TextBox x:Name="txtDiscount" 
                            Grid.Column="4"
                            materialDesign:HintAssist.Hint="Discount"
                            Style="{StaticResource ModernTextBox}"
                            Text="0"/>

                    <Button x:Name="btnAdd" 
                           Grid.Column="5"
                           Content="ADD"
                           Style="{StaticResource ModernButton}"
                           Click="BtnAdd_Click"
                           Margin="10,5,0,5"/>
                </Grid>

                <!-- Second Row -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox x:Name="txtCustomer" 
                            Grid.Column="0"
                            materialDesign:HintAssist.Hint="Customer Name"
                            Style="{StaticResource ModernTextBox}"/>

                    <ComboBox x:Name="cmbBusinessType" 
                             Grid.Column="1"
                             materialDesign:HintAssist.Hint="Business Type"
                             Style="{StaticResource ModernComboBox}"
                             DisplayMemberPath="Value"/>

                    <ComboBox x:Name="cmbSaleType" 
                             Grid.Column="2"
                             materialDesign:HintAssist.Hint="Sale Type"
                             Style="{StaticResource ModernComboBox}"
                             DisplayMemberPath="Value"/>

                    <CheckBox x:Name="chkPercentageDiscount" 
                             Grid.Column="3"
                             Content="% Discount"
                             VerticalAlignment="Center"
                             Margin="10,0,0,0"/>
                </Grid>
            </Grid>
        </materialDesign:Card>

        <!-- Items Grid -->
        <materialDesign:Card Grid.Row="2" Margin="0,0,0,10" Padding="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="Invoice Items" Style="{StaticResource SubtitleText}" Margin="0"/>
                    <Button x:Name="btnRemove" 
                           Content="REMOVE SELECTED"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Click="BtnRemove_Click"
                           Margin="20,0,0,0"/>
                </StackPanel>

                <DataGrid x:Name="dgItems" 
                         Grid.Row="1"
                         Style="{StaticResource ModernDataGrid}"
                         ItemsSource="{Binding InvoiceItems}">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="#" Binding="{Binding Index}" Width="50"/>
                        <DataGridTextColumn Header="Item Name" Binding="{Binding ItemName}" Width="*"/>
                        <DataGridTextColumn Header="Unit Price" Binding="{Binding UnitPrice, StringFormat=C}" Width="100"/>
                        <DataGridTextColumn Header="Quantity" Binding="{Binding Quantity}" Width="80"/>
                        <DataGridTextColumn Header="Sub Total" Binding="{Binding SubTotal, StringFormat=C}" Width="100"/>
                        <DataGridTextColumn Header="Discount" Binding="{Binding Discount, StringFormat=C}" Width="100"/>
                        <DataGridTextColumn Header="Total" Binding="{Binding Price, StringFormat=C}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- Totals and Actions -->
        <materialDesign:Card Grid.Row="3" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="300"/>
                    <ColumnDefinition Width="200"/>
                </Grid.ColumnDefinitions>

                <!-- Payment Method -->
                <StackPanel Grid.Column="0" VerticalAlignment="Center">
                    <ComboBox x:Name="cmbPaymentMethod" 
                             materialDesign:HintAssist.Hint="Payment Method"
                             Style="{StaticResource ModernComboBox}"
                             DisplayMemberPath="Value"
                             Width="200"
                             HorizontalAlignment="Left"/>
                </StackPanel>

                <!-- Totals -->
                <Grid Grid.Column="1" Margin="20,0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Sub Total:" Margin="0,5"/>
                    <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtSubTotal" IsReadOnly="True" Margin="10,5,0,5"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Discount:" Margin="0,5"/>
                    <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtTotalDiscount" Margin="10,5,0,5"/>

                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Total:" Margin="0,5" FontWeight="Bold"/>
                    <TextBox Grid.Row="2" Grid.Column="1" x:Name="txtTotal" IsReadOnly="True" Margin="10,5,0,5" FontWeight="Bold"/>

                    <TextBlock Grid.Row="3" Grid.Column="0" Text="Payment:" Margin="0,5"/>
                    <TextBox Grid.Row="3" Grid.Column="1" x:Name="txtPayment" Margin="10,5,0,5" KeyDown="TxtPayment_KeyDown"/>

                    <TextBlock Grid.Row="4" Grid.Column="0" Text="Balance:" Margin="0,5"/>
                    <TextBox Grid.Row="4" Grid.Column="1" x:Name="txtBalance" IsReadOnly="True" Margin="10,5,0,5"/>
                </Grid>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="2" VerticalAlignment="Center">
                    <Button x:Name="btnSave" 
                           Content="SAVE"
                           Style="{StaticResource ModernButton}"
                           Click="BtnSave_Click"
                           Margin="0,5"/>

                    <Button x:Name="btnPrintAndSave"
                           Content="PRINT &amp; SAVE"
                           Style="{StaticResource ModernButton}"
                           Click="BtnPrintAndSave_Click"
                           Margin="0,5"/>

                    <Button x:Name="btnClear" 
                           Content="CLEAR"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Click="BtnClear_Click"
                           Margin="0,5"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
