using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using GroceryERP.Models;
using GroceryERP.Services;

namespace GroceryERP.Views
{
    public partial class PurchaseInvoiceWindow : UserControl, INotifyPropertyChanged
    {
        private readonly IItemService _itemService;
        private readonly ISupplierService _supplierService;
        private readonly IPurchaseInvoiceService _purchaseInvoiceService;
        private readonly IMetaDataService _metaDataService;

        private ObservableCollection<PurchaseInvoiceRecordViewModel> _invoiceItems;
        private Item? _selectedItem;
        private Supplier? _selectedSupplier;

        public ObservableCollection<PurchaseInvoiceRecordViewModel> InvoiceItems
        {
            get => _invoiceItems;
            set
            {
                _invoiceItems = value;
                OnPropertyChanged();
            }
        }

        public PurchaseInvoiceWindow(IItemService itemService, ISupplierService supplierService, 
            IPurchaseInvoiceService purchaseInvoiceService, IMetaDataService metaDataService)
        {
            InitializeComponent();
            
            _itemService = itemService;
            _supplierService = supplierService;
            _purchaseInvoiceService = purchaseInvoiceService;
            _metaDataService = metaDataService;
            
            _invoiceItems = new ObservableCollection<PurchaseInvoiceRecordViewModel>();
            
            DataContext = this;
            
            InitializeAsync();
        }

        private async void InitializeAsync()
        {
            await LoadComboBoxData();
            ClearForm();
            txtBarcode.Focus();
        }

        private async Task LoadComboBoxData()
        {
            try
            {
                // Load Payment Methods
                var paymentMethods = await _metaDataService.GetMetaDataByCategoryAsync("PaymentMethod");
                cmbPaymentMethod.ItemsSource = paymentMethods;
                cmbPaymentMethod.SelectedIndex = 0; // Default to first item (Cash)
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading data: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void TxtBarcode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                await SearchItemByBarcode();
            }
        }

        private async Task SearchItemByBarcode()
        {
            try
            {
                var barcode = txtBarcode.Text.Trim();
                if (string.IsNullOrEmpty(barcode))
                    return;

                _selectedItem = await _itemService.GetItemByBarcodeAsync(barcode);
                
                if (_selectedItem == null)
                {
                    _selectedItem = await _itemService.GetItemByItemCodeAsync(barcode);
                }

                if (_selectedItem != null)
                {
                    txtItemName.Text = _selectedItem.ItemName;
                    txtCost.Text = _selectedItem.ItemCost.ToString("F2");
                    txtSellingPrice.Text = _selectedItem.SellingPrice.ToString("F2");
                    txtQuantity.Focus();
                }
                else
                {
                    MessageBox.Show("Item not found!", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    ClearItemFields();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error searching item: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void TxtCost_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                txtQuantity.Focus();
            }
        }

        private void TxtQuantity_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                AddItemToInvoice();
            }
        }

        private void TxtPayment_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                CalculateBalance();
            }
        }

        private void BtnAdd_Click(object sender, RoutedEventArgs e)
        {
            AddItemToInvoice();
        }

        private void AddItemToInvoice()
        {
            try
            {
                if (!ValidateItemInput())
                    return;

                var quantity = decimal.Parse(txtQuantity.Text);
                var itemCost = decimal.Parse(txtCost.Text);
                var sellingPrice = decimal.Parse(txtSellingPrice.Text);

                // Calculate amounts
                var subTotal = quantity * itemCost;
                var totalAmount = subTotal; // No discount in purchase for now

                // Create invoice record
                var record = new PurchaseInvoiceRecordViewModel
                {
                    Index = InvoiceItems.Count + 1,
                    ItemId = _selectedItem?.Id ?? 0,
                    ItemCode = _selectedItem?.ItemCode ?? "0",
                    ItemName = _selectedItem?.ItemName ?? txtItemName.Text,
                    ItemCost = itemCost,
                    Quantity = quantity,
                    SubTotal = subTotal,
                    TotalAmount = totalAmount,
                    SellingPrice = sellingPrice
                };

                InvoiceItems.Add(record);
                UpdateTotals();
                ClearItemFields();
                txtBarcode.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error adding item: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateItemInput()
        {
            if (string.IsNullOrEmpty(txtItemName.Text))
            {
                MessageBox.Show("Please select an item!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!decimal.TryParse(txtCost.Text, out _) || decimal.Parse(txtCost.Text) <= 0)
            {
                MessageBox.Show("Please enter a valid cost price!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!decimal.TryParse(txtQuantity.Text, out _) || decimal.Parse(txtQuantity.Text) <= 0)
            {
                MessageBox.Show("Please enter a valid quantity!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!decimal.TryParse(txtSellingPrice.Text, out _) || decimal.Parse(txtSellingPrice.Text) <= 0)
            {
                MessageBox.Show("Please enter a valid selling price!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private void UpdateTotals()
        {
            var subTotal = InvoiceItems.Sum(i => i.SubTotal);
            var discount = decimal.TryParse(txtDiscount.Text, out var d) ? d : 0;
            var total = subTotal - discount;

            txtSubTotal.Text = subTotal.ToString("F2");
            txtTotal.Text = total.ToString("F2");

            CalculateBalance();
        }

        private void CalculateBalance()
        {
            if (decimal.TryParse(txtTotal.Text, out var total) && 
                decimal.TryParse(txtPayment.Text, out var payment))
            {
                var balance = total - payment;
                txtBalance.Text = balance.ToString("F2");
            }
        }

        private void BtnRemove_Click(object sender, RoutedEventArgs e)
        {
            if (dgItems.SelectedItem is PurchaseInvoiceRecordViewModel selectedItem)
            {
                InvoiceItems.Remove(selectedItem);
                
                // Update indices
                for (int i = 0; i < InvoiceItems.Count; i++)
                {
                    InvoiceItems[i].Index = i + 1;
                }
                
                UpdateTotals();
            }
            else
            {
                MessageBox.Show("Please select an item to remove!", "Information", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            await SaveInvoice();
        }

        private async Task SaveInvoice()
        {
            try
            {
                if (!ValidateInvoice())
                    return;

                // Get or create supplier
                var supplier = await GetOrCreateSupplier();
                if (supplier == null)
                    return;

                // Create purchase invoice
                var purchaseInvoice = new PurchaseInvoice
                {
                    Date = dpDate.SelectedDate ?? DateTime.Now,
                    DueDate = dpDueDate.SelectedDate,
                    SupplierId = supplier.Id,
                    SupplierName = supplier.Name,
                    InvoiceNo = txtInvoiceNo.Text.Trim(),
                    PaymentMethodId = ((MetaData)cmbPaymentMethod.SelectedItem).Id,
                    TotalAmount = decimal.Parse(txtTotal.Text),
                    Payment = decimal.Parse(txtPayment.Text),
                    Balance = Math.Abs(decimal.Parse(txtBalance.Text)),
                    Discount = decimal.Parse(txtDiscount.Text),
                    PurchaseInvoiceRecords = InvoiceItems.Select(item => new PurchaseInvoiceRecord
                    {
                        ItemId = item.ItemId,
                        ItemCode = item.ItemCode,
                        Quantity = item.Quantity,
                        ItemCost = item.ItemCost,
                        SellingPrice = item.SellingPrice,
                        SubTotal = item.SubTotal,
                        TotalAmount = item.TotalAmount,
                        Date = DateTime.Now
                    }).ToList()
                };

                var invoiceNumber = await _purchaseInvoiceService.CreatePurchaseInvoiceAsync(purchaseInvoice);
                
                if (invoiceNumber != null)
                {
                    MessageBox.Show($"Purchase invoice saved successfully! Invoice No: {invoiceNumber}", 
                        "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    ClearForm();
                }
                else
                {
                    MessageBox.Show("Failed to save purchase invoice!", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving invoice: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInvoice()
        {
            if (InvoiceItems.Count == 0)
            {
                MessageBox.Show("Please add at least one item!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!decimal.TryParse(txtPayment.Text, out _))
            {
                MessageBox.Show("Please enter a valid payment amount!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private async Task<Supplier?> GetOrCreateSupplier()
        {
            try
            {
                var supplierName = txtSupplier.Text.Trim();
                
                if (string.IsNullOrEmpty(supplierName))
                {
                    MessageBox.Show("Please enter supplier name!", "Validation Error", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return null;
                }

                // Search for existing supplier
                var suppliers = await _supplierService.SearchSuppliersByNameAsync(supplierName);
                var existingSupplier = suppliers.FirstOrDefault(s => 
                    s.Name.Equals(supplierName, StringComparison.OrdinalIgnoreCase));

                if (existingSupplier != null)
                {
                    return existingSupplier;
                }

                // Create new supplier
                var newSupplier = new Supplier
                {
                    SupplierCode = $"SUP{DateTime.Now:yyyyMMddHHmmss}",
                    Name = supplierName
                };

                var created = await _supplierService.CreateSupplierAsync(newSupplier);
                return created ? newSupplier : null;
            }
            catch
            {
                return null;
            }
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("Are you sure you want to clear all data?", "Confirm Clear", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                ClearForm();
            }
        }

        private void ClearForm()
        {
            InvoiceItems.Clear();
            ClearItemFields();
            txtSupplier.Clear();
            txtInvoiceNo.Clear();
            txtNotes.Clear();
            txtSubTotal.Clear();
            txtDiscount.Text = "0";
            txtTotal.Clear();
            txtPayment.Clear();
            txtBalance.Clear();
            dpDate.SelectedDate = DateTime.Today;
            dpDueDate.SelectedDate = DateTime.Today.AddDays(30);
            
            // Reset combo boxes to default
            if (cmbPaymentMethod.Items.Count > 0) cmbPaymentMethod.SelectedIndex = 0;
            
            txtBarcode.Focus();
        }

        private void ClearItemFields()
        {
            txtBarcode.Clear();
            txtItemName.Clear();
            txtCost.Clear();
            txtQuantity.Clear();
            txtSellingPrice.Clear();
            _selectedItem = null;
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // ViewModel for displaying purchase invoice items in the DataGrid
    public class PurchaseInvoiceRecordViewModel
    {
        public int Index { get; set; }
        public int ItemId { get; set; }
        public string ItemCode { get; set; } = string.Empty;
        public string ItemName { get; set; } = string.Empty;
        public decimal ItemCost { get; set; }
        public decimal Quantity { get; set; }
        public decimal SubTotal { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal SellingPrice { get; set; }
    }
}
