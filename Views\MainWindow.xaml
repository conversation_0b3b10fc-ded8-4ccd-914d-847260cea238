<Window x:Class="GroceryERP.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Grocery ERP - Main Dashboard" 
        Height="800" 
        Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="10" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Store" Width="32" Height="32" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="Grocery ERP" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              VerticalAlignment="Center" 
                              Margin="10,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,20,0">
                    <TextBlock Text="Welcome, " VerticalAlignment="Center"/>
                    <TextBlock x:Name="lblUsername" FontWeight="Bold" VerticalAlignment="Center"/>
                    <TextBlock Text=" | Counter: " VerticalAlignment="Center" Margin="10,0,0,0"/>
                    <TextBlock x:Name="lblCounter" FontWeight="Bold" VerticalAlignment="Center"/>
                </StackPanel>

                <Button Grid.Column="2" 
                       Content="LOGOUT" 
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Click="BtnLogout_Click"/>
            </Grid>
        </materialDesign:Card>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="10,0,10,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Navigation Menu -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,10,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <TextBlock Text="SALES" Style="{StaticResource SubtitleText}" Margin="10,10,10,5"/>
                        
                        <Button x:Name="btnSalesInvoice"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Margin="5"
                               Click="BtnSalesInvoice_Click">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Receipt" Width="20" Height="20" Margin="0,0,10,0"/>
                                    <TextBlock Text="Sales Invoice"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button x:Name="btnPurchaseInvoice"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Margin="5"
                               Click="BtnPurchaseInvoice_Click">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileDocumentEdit" Width="20" Height="20" Margin="0,0,10,0"/>
                                    <TextBlock Text="Purchase Invoice"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button x:Name="btnManageSales"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Margin="5">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileDocument" Width="20" Height="20" Margin="0,0,10,0"/>
                                    <TextBlock Text="Manage Sales"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Separator Margin="5,10"/>
                        
                        <TextBlock Text="INVENTORY" Style="{StaticResource SubtitleText}" Margin="10,10,10,5"/>
                        
                        <Button x:Name="btnManageItems"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Margin="5"
                               Click="BtnManageItems_Click">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Package" Width="20" Height="20" Margin="0,0,10,0"/>
                                    <TextBlock Text="Manage Items"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button x:Name="btnManageStock"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Margin="5">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Warehouse" Width="20" Height="20" Margin="0,0,10,0"/>
                                    <TextBlock Text="Stock Management"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Separator Margin="5,10"/>
                        
                        <TextBlock Text="CUSTOMERS" Style="{StaticResource SubtitleText}" Margin="10,10,10,5"/>
                        
                        <Button x:Name="btnManageCustomers"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Margin="5">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Account" Width="20" Height="20" Margin="0,0,10,0"/>
                                    <TextBlock Text="Manage Customers"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Separator Margin="5,10"/>
                        
                        <TextBlock Text="REPORTS" Style="{StaticResource SubtitleText}" Margin="10,10,10,5"/>
                        
                        <Button x:Name="btnSalesReports"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Margin="5">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ChartLine" Width="20" Height="20" Margin="0,0,10,0"/>
                                    <TextBlock Text="Sales Reports"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button x:Name="btnInventoryReports"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Margin="5">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ChartBar" Width="20" Height="20" Margin="0,0,10,0"/>
                                    <TextBlock Text="Inventory Reports"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>

            <!-- Main Content Area -->
            <materialDesign:Card Grid.Column="1" Margin="10,0,0,0">
                <Grid x:Name="MainContentArea">
                    <!-- Dashboard Content -->
                    <StackPanel x:Name="DashboardContent" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <materialDesign:PackIcon Kind="ViewDashboard" Width="100" Height="100" 
                                               Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        <TextBlock Text="Welcome to Grocery ERP" 
                                  Style="{StaticResource TitleText}" 
                                  HorizontalAlignment="Center" 
                                  Margin="0,20,0,10"/>
                        <TextBlock Text="Select an option from the menu to get started" 
                                  HorizontalAlignment="Center" 
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>
        </Grid>

        <!-- Status Bar -->
        <StatusBar Grid.Row="2" Background="{DynamicResource MaterialDesignDarkBackground}">
            <StatusBarItem>
                <TextBlock x:Name="lblStatus" Text="Ready" Foreground="White"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock x:Name="lblDateTime" Foreground="White"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
