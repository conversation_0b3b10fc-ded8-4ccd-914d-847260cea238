using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GroceryERP.Models
{
    public class SalesInvoiceRecord : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string ItemCode { get; set; } = string.Empty;

        [StringLength(50)]
        public string? SalesInvoiceNo { get; set; }

        public int ItemId { get; set; }
        public virtual Item Item { get; set; } = null!;

        [Column(TypeName = "decimal(18,3)")]
        public decimal Quantity { get; set; }

        [StringLength(20)]
        public string? DisplayQuantity { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        // qty * unit price
        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }

        // discount amount received
        [Column(TypeName = "decimal(18,2)")]
        public decimal Discount { get; set; }

        // subtotal - discount
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal ItemCost { get; set; }

        public DateTime Date { get; set; } = DateTime.Now;

        public int SaleTypeId { get; set; }
        public virtual MetaData SaleType { get; set; } = null!;

        public int BusinessTypeId { get; set; }
        public virtual MetaData BusinessType { get; set; } = null!;

        // When itemcode = 0 this will be available (no item name in sales invoice)
        [StringLength(200)]
        public string? CustomName { get; set; }

        [StringLength(10)]
        public string Counter { get; set; } = string.Empty;

        public int SalesInvoiceId { get; set; }
        public virtual SalesInvoice SalesInvoice { get; set; } = null!;

        // Calculated property for single discount (for display)
        [NotMapped]
        public decimal SingleDiscount => Quantity > 0 ? Discount / Quantity : 0;

        // Display properties for different languages
        [NotMapped]
        public string ItemName => Item?.ItemName ?? CustomName ?? string.Empty;

        [NotMapped]
        public string ItemNameSinhala => Item?.ItemNameSinhala ?? CustomName ?? string.Empty;
    }
}
