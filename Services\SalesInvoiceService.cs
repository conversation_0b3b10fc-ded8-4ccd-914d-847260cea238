using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using GroceryERP.Data;
using GroceryERP.Models;

namespace GroceryERP.Services
{
    public class SalesInvoiceService : ISalesInvoiceService
    {
        private readonly GroceryERPContext _context;
        private readonly IItemService _itemService;

        public SalesInvoiceService(GroceryERPContext context, IItemService itemService)
        {
            _context = context;
            _itemService = itemService;
        }

        public async Task<IEnumerable<SalesInvoice>> GetAllSalesInvoicesAsync()
        {
            return await _context.SalesInvoices
                .Include(si => si.Customer)
                .Include(si => si.PaymentMethod)
                .Include(si => si.Status)
                .Include(si => si.SalesInvoiceRecords)
                    .ThenInclude(sir => sir.Item)
                .Where(si => si.IsActive)
                .OrderByDescending(si => si.CreatedDate)
                .ToListAsync();
        }

        public async Task<SalesInvoice?> GetSalesInvoiceByIdAsync(int id)
        {
            return await _context.SalesInvoices
                .Include(si => si.Customer)
                .Include(si => si.PaymentMethod)
                .Include(si => si.Status)
                .Include(si => si.SalesInvoiceRecords)
                    .ThenInclude(sir => sir.Item)
                .Include(si => si.SalesInvoiceRecords)
                    .ThenInclude(sir => sir.SaleType)
                .Include(si => si.SalesInvoiceRecords)
                    .ThenInclude(sir => sir.BusinessType)
                .FirstOrDefaultAsync(si => si.Id == id && si.IsActive);
        }

        public async Task<SalesInvoice?> GetSalesInvoiceByNumberAsync(string invoiceNumber)
        {
            return await _context.SalesInvoices
                .Include(si => si.Customer)
                .Include(si => si.PaymentMethod)
                .Include(si => si.Status)
                .Include(si => si.SalesInvoiceRecords)
                    .ThenInclude(sir => sir.Item)
                .FirstOrDefaultAsync(si => si.SalesInvoiceNo == invoiceNumber && si.IsActive);
        }

        public async Task<IEnumerable<SalesInvoice>> GetSalesInvoicesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.SalesInvoices
                .Include(si => si.Customer)
                .Include(si => si.PaymentMethod)
                .Include(si => si.Status)
                .Where(si => si.IsActive && si.Date >= startDate && si.Date <= endDate)
                .OrderByDescending(si => si.Date)
                .ToListAsync();
        }

        public async Task<IEnumerable<SalesInvoice>> GetSalesInvoicesByCustomerAsync(int customerId)
        {
            return await _context.SalesInvoices
                .Include(si => si.Customer)
                .Include(si => si.PaymentMethod)
                .Include(si => si.Status)
                .Where(si => si.IsActive && si.CustomerId == customerId)
                .OrderByDescending(si => si.Date)
                .ToListAsync();
        }

        public async Task<string?> CreateSalesInvoiceAsync(SalesInvoice salesInvoice)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Generate invoice number
                var invoiceNumber = await GenerateInvoiceNumberAsync();
                salesInvoice.SalesInvoiceNo = invoiceNumber;

                // Set status based on payment
                if (salesInvoice.TotalAmount <= salesInvoice.Payment)
                {
                    var completedStatus = await _context.MetaData
                        .FirstOrDefaultAsync(md => md.Category == "PaymentStatus" && md.Value == "Completed");
                    salesInvoice.StatusId = completedStatus?.Id ?? 7; // Default to 7 if not found
                }
                else if (salesInvoice.Payment == 0)
                {
                    var pendingStatus = await _context.MetaData
                        .FirstOrDefaultAsync(md => md.Category == "PaymentStatus" && md.Value == "Pending");
                    salesInvoice.StatusId = pendingStatus?.Id ?? 5; // Default to 5 if not found
                }
                else
                {
                    var partialStatus = await _context.MetaData
                        .FirstOrDefaultAsync(md => md.Category == "PaymentStatus" && md.Value == "Partially paid");
                    salesInvoice.StatusId = partialStatus?.Id ?? 6; // Default to 6 if not found
                }

                // Set invoice number for all records
                foreach (var record in salesInvoice.SalesInvoiceRecords)
                {
                    record.SalesInvoiceNo = invoiceNumber;
                    record.Counter = salesInvoice.Counter;
                }

                _context.SalesInvoices.Add(salesInvoice);
                await _context.SaveChangesAsync();

                // Deduct from stock
                var stockDeductionSuccess = await DeductFromStockAsync(salesInvoice);
                if (!stockDeductionSuccess)
                {
                    await transaction.RollbackAsync();
                    return null;
                }

                await transaction.CommitAsync();
                return invoiceNumber;
            }
            catch
            {
                await transaction.RollbackAsync();
                return null;
            }
        }

        public async Task<bool> UpdateSalesInvoiceAsync(SalesInvoice salesInvoice)
        {
            try
            {
                _context.SalesInvoices.Update(salesInvoice);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteSalesInvoiceAsync(int id)
        {
            try
            {
                var salesInvoice = await _context.SalesInvoices.FindAsync(id);
                if (salesInvoice == null)
                    return false;

                // Soft delete
                salesInvoice.IsActive = false;
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<decimal> GetTotalSalesForDateAsync(DateTime date)
        {
            var startDate = date.Date;
            var endDate = date.Date.AddDays(1);

            return await _context.SalesInvoices
                .Where(si => si.IsActive && si.Date >= startDate && si.Date < endDate)
                .SumAsync(si => si.TotalAmount);
        }

        public async Task<decimal> GetTotalSalesForDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.SalesInvoices
                .Where(si => si.IsActive && si.Date >= startDate && si.Date <= endDate)
                .SumAsync(si => si.TotalAmount);
        }

        public async Task<IEnumerable<SalesInvoice>> GetPendingPaymentsAsync()
        {
            var pendingStatus = await _context.MetaData
                .FirstOrDefaultAsync(md => md.Category == "PaymentStatus" && md.Value == "Pending");
            var partialStatus = await _context.MetaData
                .FirstOrDefaultAsync(md => md.Category == "PaymentStatus" && md.Value == "Partially paid");

            var pendingStatusId = pendingStatus?.Id ?? 5;
            var partialStatusId = partialStatus?.Id ?? 6;

            return await _context.SalesInvoices
                .Include(si => si.Customer)
                .Include(si => si.Status)
                .Where(si => si.IsActive &&
                           (si.StatusId == pendingStatusId ||
                            si.StatusId == partialStatusId))
                .OrderBy(si => si.DueDate)
                .ToListAsync();
        }

        private async Task<string> GenerateInvoiceNumberAsync()
        {
            // Simple invoice number generation - you might want to implement a more sophisticated sequence
            var lastInvoice = await _context.SalesInvoices
                .OrderByDescending(si => si.Id)
                .FirstOrDefaultAsync();

            var nextNumber = 1;
            if (lastInvoice != null)
            {
                // Extract number from last invoice (assuming format like "SI-000001")
                var lastNumber = lastInvoice.SalesInvoiceNo.Split('-').LastOrDefault();
                if (int.TryParse(lastNumber, out var parsed))
                {
                    nextNumber = parsed + 1;
                }
            }

            return $"SI-{nextNumber:D6}";
        }

        private async Task<bool> DeductFromStockAsync(SalesInvoice salesInvoice)
        {
            try
            {
                foreach (var record in salesInvoice.SalesInvoiceRecords)
                {
                    var item = await _itemService.GetItemByItemCodeAsync(record.ItemCode);
                    if (item != null && item.ManageStock)
                    {
                        // Check if it's a return sale (negative quantity)
                        var returnSaleType = await _context.MetaData
                            .FirstOrDefaultAsync(md => md.Category == "SaleType" && md.Value == "Return");

                        var returnSaleTypeId = returnSaleType?.Id ?? 9;
                        var isReturn = record.SaleTypeId == returnSaleTypeId;
                        
                        if (!isReturn && item.Quantity < record.Quantity)
                        {
                            return false; // Insufficient stock
                        }

                        if (isReturn)
                        {
                            // Add back to stock for returns
                            await _itemService.AddToStockAsync(record.ItemCode, Math.Abs(record.Quantity));
                        }
                        else
                        {
                            // Deduct from stock for sales
                            await _itemService.DeductFromStockAsync(record.ItemCode, record.Quantity);
                        }
                    }
                }
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
