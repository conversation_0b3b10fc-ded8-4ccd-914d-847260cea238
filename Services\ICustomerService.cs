using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GroceryERP.Models;

namespace GroceryERP.Services
{
    public interface ICustomerService
    {
        Task<IEnumerable<Customer>> GetAllCustomersAsync();
        Task<Customer?> GetCustomerByIdAsync(int id);
        Task<Customer?> GetCustomerByCodeAsync(string customerCode);
        Task<Customer?> GetDefaultCustomerAsync();
        Task<IEnumerable<Customer>> SearchCustomersByNameAsync(string name);
        Task<bool> CreateCustomerAsync(Customer customer);
        Task<bool> UpdateCustomerAsync(Customer customer);
        Task<bool> DeleteCustomerAsync(int id);
    }
}
