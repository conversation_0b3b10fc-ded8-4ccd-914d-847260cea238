using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using GroceryERP.Data;
using GroceryERP.Models;

namespace GroceryERP.Services
{
    public class PurchaseInvoiceService : IPurchaseInvoiceService
    {
        private readonly GroceryERPContext _context;
        private readonly IItemService _itemService;

        public PurchaseInvoiceService(GroceryERPContext context, IItemService itemService)
        {
            _context = context;
            _itemService = itemService;
        }

        public async Task<IEnumerable<PurchaseInvoice>> GetAllPurchaseInvoicesAsync()
        {
            return await _context.PurchaseInvoices
                .Include(pi => pi.Supplier)
                .Include(pi => pi.PaymentMethod)
                .Include(pi => pi.Status)
                .Include(pi => pi.PurchaseInvoiceRecords)
                    .ThenInclude(pir => pir.Item)
                .Where(pi => pi.IsActive)
                .OrderByDescending(pi => pi.CreatedDate)
                .ToListAsync();
        }

        public async Task<PurchaseInvoice?> GetPurchaseInvoiceByIdAsync(int id)
        {
            return await _context.PurchaseInvoices
                .Include(pi => pi.Supplier)
                .Include(pi => pi.PaymentMethod)
                .Include(pi => pi.Status)
                .Include(pi => pi.PurchaseInvoiceRecords)
                    .ThenInclude(pir => pir.Item)
                .FirstOrDefaultAsync(pi => pi.Id == id && pi.IsActive);
        }

        public async Task<PurchaseInvoice?> GetPurchaseInvoiceByNumberAsync(string invoiceNumber)
        {
            return await _context.PurchaseInvoices
                .Include(pi => pi.Supplier)
                .Include(pi => pi.PaymentMethod)
                .Include(pi => pi.Status)
                .Include(pi => pi.PurchaseInvoiceRecords)
                    .ThenInclude(pir => pir.Item)
                .FirstOrDefaultAsync(pi => pi.PurchaseInvoiceNo == invoiceNumber && pi.IsActive);
        }

        public async Task<IEnumerable<PurchaseInvoice>> GetPurchaseInvoicesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.PurchaseInvoices
                .Include(pi => pi.Supplier)
                .Include(pi => pi.PaymentMethod)
                .Include(pi => pi.Status)
                .Where(pi => pi.IsActive && pi.Date >= startDate && pi.Date <= endDate)
                .OrderByDescending(pi => pi.Date)
                .ToListAsync();
        }

        public async Task<IEnumerable<PurchaseInvoice>> GetPurchaseInvoicesBySupplierAsync(int supplierId)
        {
            return await _context.PurchaseInvoices
                .Include(pi => pi.Supplier)
                .Include(pi => pi.PaymentMethod)
                .Include(pi => pi.Status)
                .Where(pi => pi.IsActive && pi.SupplierId == supplierId)
                .OrderByDescending(pi => pi.Date)
                .ToListAsync();
        }

        public async Task<string?> CreatePurchaseInvoiceAsync(PurchaseInvoice purchaseInvoice)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Generate invoice number
                var invoiceNumber = await GenerateInvoiceNumberAsync();
                purchaseInvoice.PurchaseInvoiceNo = invoiceNumber;

                // Set status based on payment
                if (purchaseInvoice.TotalAmount <= purchaseInvoice.Payment)
                {
                    var completedStatus = await _context.MetaData
                        .FirstOrDefaultAsync(md => md.Category == "PaymentStatus" && md.Value == "Completed");
                    purchaseInvoice.StatusId = completedStatus?.Id ?? 7;
                }
                else if (purchaseInvoice.Payment == 0)
                {
                    var pendingStatus = await _context.MetaData
                        .FirstOrDefaultAsync(md => md.Category == "PaymentStatus" && md.Value == "Pending");
                    purchaseInvoice.StatusId = pendingStatus?.Id ?? 5;
                }
                else
                {
                    var partialStatus = await _context.MetaData
                        .FirstOrDefaultAsync(md => md.Category == "PaymentStatus" && md.Value == "Partially paid");
                    purchaseInvoice.StatusId = partialStatus?.Id ?? 6;
                }

                // Set invoice number for all records
                foreach (var record in purchaseInvoice.PurchaseInvoiceRecords)
                {
                    record.PurchaseInvoiceNo = invoiceNumber;
                }

                _context.PurchaseInvoices.Add(purchaseInvoice);
                await _context.SaveChangesAsync();

                // Add to stock
                var stockAdditionSuccess = await AddToStockAsync(purchaseInvoice);
                if (!stockAdditionSuccess)
                {
                    await transaction.RollbackAsync();
                    return null;
                }

                await transaction.CommitAsync();
                return invoiceNumber;
            }
            catch
            {
                await transaction.RollbackAsync();
                return null;
            }
        }

        public async Task<bool> UpdatePurchaseInvoiceAsync(PurchaseInvoice purchaseInvoice)
        {
            try
            {
                _context.PurchaseInvoices.Update(purchaseInvoice);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeletePurchaseInvoiceAsync(int id)
        {
            try
            {
                var purchaseInvoice = await _context.PurchaseInvoices.FindAsync(id);
                if (purchaseInvoice == null)
                    return false;

                // Soft delete
                purchaseInvoice.IsActive = false;
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<decimal> GetTotalPurchasesForDateAsync(DateTime date)
        {
            var startDate = date.Date;
            var endDate = date.Date.AddDays(1);

            return await _context.PurchaseInvoices
                .Where(pi => pi.IsActive && pi.Date >= startDate && pi.Date < endDate)
                .SumAsync(pi => pi.TotalAmount);
        }

        public async Task<decimal> GetTotalPurchasesForDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.PurchaseInvoices
                .Where(pi => pi.IsActive && pi.Date >= startDate && pi.Date <= endDate)
                .SumAsync(pi => pi.TotalAmount);
        }

        public async Task<IEnumerable<PurchaseInvoice>> GetPendingPaymentsAsync()
        {
            var pendingStatus = await _context.MetaData
                .FirstOrDefaultAsync(md => md.Category == "PaymentStatus" && md.Value == "Pending");
            var partialStatus = await _context.MetaData
                .FirstOrDefaultAsync(md => md.Category == "PaymentStatus" && md.Value == "Partially paid");

            var pendingStatusId = pendingStatus?.Id ?? 5;
            var partialStatusId = partialStatus?.Id ?? 6;

            return await _context.PurchaseInvoices
                .Include(pi => pi.Supplier)
                .Include(pi => pi.Status)
                .Where(pi => pi.IsActive &&
                           (pi.StatusId == pendingStatusId ||
                            pi.StatusId == partialStatusId))
                .OrderBy(pi => pi.DueDate)
                .ToListAsync();
        }

        private async Task<string> GenerateInvoiceNumberAsync()
        {
            var sequence = await _context.Sequences
                .FirstOrDefaultAsync(s => s.Name == "PurchaseInvoice");

            if (sequence != null)
            {
                var nextNumber = sequence.GetNextNumber();
                _context.Sequences.Update(sequence);
                return nextNumber;
            }

            // Fallback if sequence not found
            var lastInvoice = await _context.PurchaseInvoices
                .OrderByDescending(pi => pi.Id)
                .FirstOrDefaultAsync();

            var nextNum = 1;
            if (lastInvoice != null)
            {
                var lastNumber = lastInvoice.PurchaseInvoiceNo.Split('-').LastOrDefault();
                if (int.TryParse(lastNumber, out var parsed))
                {
                    nextNum = parsed + 1;
                }
            }

            return $"PI-{nextNum:D6}";
        }

        private async Task<bool> AddToStockAsync(PurchaseInvoice purchaseInvoice)
        {
            try
            {
                foreach (var record in purchaseInvoice.PurchaseInvoiceRecords)
                {
                    var item = await _itemService.GetItemByItemCodeAsync(record.ItemCode);
                    if (item != null)
                    {
                        // Add to stock
                        await _itemService.AddToStockAsync(record.ItemCode, record.Quantity);
                        
                        // Update item cost and selling price if provided
                        if (record.ItemCost > 0)
                        {
                            item.ItemCost = record.ItemCost;
                        }
                        if (record.SellingPrice > 0)
                        {
                            item.SellingPrice = record.SellingPrice;
                        }
                        
                        await _context.SaveChangesAsync();
                    }
                }
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
