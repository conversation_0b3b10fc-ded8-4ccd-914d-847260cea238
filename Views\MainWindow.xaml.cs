using System;
using System.Windows;
using System.Windows.Threading;
using GroceryERP.Models;

namespace GroceryERP.Views
{
    public partial class MainWindow : Window
    {
        private DispatcherTimer _timer;

        public MainWindow()
        {
            InitializeComponent();
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            // Set user information
            if (Application.Current.Properties["CurrentUser"] is User currentUser)
            {
                lblUsername.Text = currentUser.FullName;
            }

            // Set counter (from configuration)
            lblCounter.Text = "001"; // This should come from configuration

            // Initialize timer for status bar
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;
            _timer.Start();

            // Update status
            lblStatus.Text = "Ready";
        }

        private void Timer_Tick(object? sender, EventArgs e)
        {
            lblDateTime.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        private void BtnSalesInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var salesInvoiceWindow = App.GetService<SalesInvoiceWindow>();

                // Clear the main content area and show the sales invoice window
                MainContentArea.Children.Clear();
                MainContentArea.Children.Add(salesInvoiceWindow);

                lblStatus.Text = "Sales Invoice";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening Sales Invoice: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnPurchaseInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var purchaseInvoiceWindow = App.GetService<PurchaseInvoiceWindow>();

                // Clear the main content area and show the purchase invoice window
                MainContentArea.Children.Clear();
                MainContentArea.Children.Add(purchaseInvoiceWindow);

                lblStatus.Text = "Purchase Invoice";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening Purchase Invoice: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnManageItems_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var itemManagementWindow = App.GetService<ItemManagementWindow>();

                // Clear the main content area and show the item management window
                MainContentArea.Children.Clear();
                MainContentArea.Children.Add(itemManagementWindow);

                lblStatus.Text = "Item Management";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening Item Management: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnLogout_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("Are you sure you want to logout?", "Confirm Logout", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // Clear current user
                Application.Current.Properties.Remove("CurrentUser");

                // Show login window
                var loginWindow = App.GetService<LoginWindow>();
                loginWindow.Show();

                // Close main window
                this.Close();
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _timer?.Stop();
            base.OnClosed(e);
        }
    }
}
