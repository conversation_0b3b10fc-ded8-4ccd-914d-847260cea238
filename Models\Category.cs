using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace GroceryERP.Models
{
    public class Category : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        // Navigation properties
        public virtual ICollection<Item> Items { get; set; } = new List<Item>();
        public virtual ICollection<SubCategory> SubCategories { get; set; } = new List<SubCategory>();
    }

    public class SubCategory : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        public int CategoryId { get; set; }
        public virtual Category Category { get; set; } = null!;

        // Navigation properties
        public virtual ICollection<Item> Items { get; set; } = new List<Item>();
    }
}
