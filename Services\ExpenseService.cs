using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using GroceryERP.Data;
using GroceryERP.Models;

namespace GroceryERP.Services
{
    public class ExpenseService : IExpenseService
    {
        private readonly GroceryERPContext _context;

        public ExpenseService(GroceryERPContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Expense>> GetAllExpensesAsync()
        {
            return await _context.Expenses
                .Include(e => e.ExpenseType)
                .Include(e => e.Supplier)
                .Where(e => e.IsActive)
                .OrderByDescending(e => e.Date)
                .ToListAsync();
        }

        public async Task<Expense?> GetExpenseByIdAsync(int id)
        {
            return await _context.Expenses
                .Include(e => e.ExpenseType)
                .Include(e => e.Supplier)
                .FirstOrDefaultAsync(e => e.Id == id && e.IsActive);
        }

        public async Task<Expense?> GetExpenseByNumberAsync(string expenseNumber)
        {
            return await _context.Expenses
                .Include(e => e.ExpenseType)
                .Include(e => e.Supplier)
                .FirstOrDefaultAsync(e => e.ExpenseNo == expenseNumber && e.IsActive);
        }

        public async Task<IEnumerable<Expense>> GetExpensesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Expenses
                .Include(e => e.ExpenseType)
                .Include(e => e.Supplier)
                .Where(e => e.IsActive && e.Date >= startDate && e.Date <= endDate)
                .OrderByDescending(e => e.Date)
                .ToListAsync();
        }

        public async Task<string?> CreateExpenseAsync(Expense expense)
        {
            try
            {
                var expenseNumber = await GenerateExpenseNumberAsync();
                expense.ExpenseNo = expenseNumber;

                _context.Expenses.Add(expense);
                await _context.SaveChangesAsync();
                return expenseNumber;
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> UpdateExpenseAsync(Expense expense)
        {
            try
            {
                _context.Expenses.Update(expense);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteExpenseAsync(int id)
        {
            try
            {
                var expense = await _context.Expenses.FindAsync(id);
                if (expense == null)
                    return false;

                expense.IsActive = false;
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<decimal> GetTotalExpensesForDateAsync(DateTime date)
        {
            var startDate = date.Date;
            var endDate = date.Date.AddDays(1);

            return await _context.Expenses
                .Where(e => e.IsActive && e.Date >= startDate && e.Date < endDate)
                .SumAsync(e => e.Amount);
        }

        public async Task<decimal> GetTotalExpensesForDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Expenses
                .Where(e => e.IsActive && e.Date >= startDate && e.Date <= endDate)
                .SumAsync(e => e.Amount);
        }

        public async Task<IEnumerable<ExpenseType>> GetAllExpenseTypesAsync()
        {
            return await _context.ExpenseTypes
                .Where(et => et.IsActive)
                .OrderBy(et => et.Name)
                .ToListAsync();
        }

        public async Task<bool> CreateExpenseTypeAsync(ExpenseType expenseType)
        {
            try
            {
                var existing = await _context.ExpenseTypes
                    .FirstOrDefaultAsync(et => et.Name == expenseType.Name);

                if (existing != null)
                    return false;

                _context.ExpenseTypes.Add(expenseType);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateExpenseTypeAsync(ExpenseType expenseType)
        {
            try
            {
                var existing = await _context.ExpenseTypes
                    .FirstOrDefaultAsync(et => et.Id != expenseType.Id && et.Name == expenseType.Name);

                if (existing != null)
                    return false;

                _context.ExpenseTypes.Update(expenseType);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteExpenseTypeAsync(int id)
        {
            try
            {
                var expenseType = await _context.ExpenseTypes.FindAsync(id);
                if (expenseType == null)
                    return false;

                var hasExpenses = await _context.Expenses
                    .AnyAsync(e => e.ExpenseTypeId == id);

                if (hasExpenses)
                {
                    expenseType.IsActive = false;
                }
                else
                {
                    _context.ExpenseTypes.Remove(expenseType);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task<string> GenerateExpenseNumberAsync()
        {
            var sequence = await _context.Sequences
                .FirstOrDefaultAsync(s => s.Name == "Expense");

            if (sequence != null)
            {
                var nextNumber = sequence.GetNextNumber();
                _context.Sequences.Update(sequence);
                return nextNumber;
            }

            // Fallback if sequence not found
            var lastExpense = await _context.Expenses
                .OrderByDescending(e => e.Id)
                .FirstOrDefaultAsync();

            var nextNum = 1;
            if (lastExpense != null)
            {
                var lastNumber = lastExpense.ExpenseNo.Split('-').LastOrDefault();
                if (int.TryParse(lastNumber, out var parsed))
                {
                    nextNum = parsed + 1;
                }
            }

            return $"EX-{nextNum:D6}";
        }
    }
}
