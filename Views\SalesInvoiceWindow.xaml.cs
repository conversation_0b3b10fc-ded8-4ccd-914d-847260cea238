using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using GroceryERP.Models;
using GroceryERP.Services;

namespace GroceryERP.Views
{
    public partial class SalesInvoiceWindow : UserControl, INotifyPropertyChanged
    {
        private readonly IItemService _itemService;
        private readonly ICustomerService _customerService;
        private readonly ISalesInvoiceService _salesInvoiceService;
        private readonly IMetaDataService _metaDataService;

        private ObservableCollection<SalesInvoiceRecordViewModel> _invoiceItems;
        private Item? _selectedItem;
        private Customer? _selectedCustomer;
        private bool _isPercentageDiscount;

        public ObservableCollection<SalesInvoiceRecordViewModel> InvoiceItems
        {
            get => _invoiceItems;
            set
            {
                _invoiceItems = value;
                OnPropertyChanged();
            }
        }

        public SalesInvoiceWindow(IItemService itemService, ICustomerService customerService, 
            ISalesInvoiceService salesInvoiceService, IMetaDataService metaDataService)
        {
            InitializeComponent();
            
            _itemService = itemService;
            _customerService = customerService;
            _salesInvoiceService = salesInvoiceService;
            _metaDataService = metaDataService;
            
            _invoiceItems = new ObservableCollection<SalesInvoiceRecordViewModel>();
            
            DataContext = this;
            
            InitializeAsync();
        }

        private async void InitializeAsync()
        {
            await LoadComboBoxData();
            ClearForm();
            txtBarcode.Focus();
        }

        private async Task LoadComboBoxData()
        {
            try
            {
                // Load Business Types
                var businessTypes = await _metaDataService.GetMetaDataByCategoryAsync("BusinessType");
                cmbBusinessType.ItemsSource = businessTypes;
                cmbBusinessType.SelectedIndex = 0; // Default to first item (Retail)

                // Load Sale Types
                var saleTypes = await _metaDataService.GetMetaDataByCategoryAsync("SaleType");
                cmbSaleType.ItemsSource = saleTypes;
                cmbSaleType.SelectedIndex = 0; // Default to first item (Sale)

                // Load Payment Methods
                var paymentMethods = await _metaDataService.GetMetaDataByCategoryAsync("PaymentMethod");
                cmbPaymentMethod.ItemsSource = paymentMethods;
                cmbPaymentMethod.SelectedIndex = 0; // Default to first item (Cash)
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading data: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void TxtBarcode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                await SearchItemByBarcode();
            }
        }

        private async Task SearchItemByBarcode()
        {
            try
            {
                var barcode = txtBarcode.Text.Trim();
                if (string.IsNullOrEmpty(barcode))
                    return;

                _selectedItem = await _itemService.GetItemByBarcodeAsync(barcode);
                
                if (_selectedItem == null)
                {
                    _selectedItem = await _itemService.GetItemByItemCodeAsync(barcode);
                }

                if (_selectedItem != null)
                {
                    await CheckItemAvailability();
                }
                else
                {
                    MessageBox.Show("Item not found!", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    ClearItemFields();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error searching item: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task CheckItemAvailability()
        {
            if (_selectedItem == null) return;

            try
            {
                // Check if it's a return sale
                var isReturn = cmbSaleType.SelectedItem is MetaData saleType && saleType.Value == "Return";
                
                if (!isReturn && _selectedItem.ManageStock && _selectedItem.Quantity <= 0)
                {
                    MessageBox.Show("Stock not available!", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    ClearItemFields();
                    return;
                }

                // Populate item fields
                txtItemName.Text = _selectedItem.ItemName;
                txtPrice.Text = _selectedItem.SellingPrice.ToString("F2");
                
                // Set discount based on business type
                SetDiscountBasedOnBusinessType();
                
                txtQuantity.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error checking item availability: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SetDiscountBasedOnBusinessType()
        {
            if (_selectedItem == null || cmbBusinessType.SelectedItem is not MetaData businessType)
                return;

            decimal discount = 0;
            
            switch (businessType.Value)
            {
                case "Retail":
                    discount = _selectedItem.RetailDiscount ?? 0;
                    break;
                case "Wholesale":
                    discount = _selectedItem.WholesaleDiscount ?? 0;
                    break;
                case "Special":
                    discount = _selectedItem.SpecialDiscount ?? 0;
                    break;
                default:
                    discount = 0;
                    break;
            }

            txtDiscount.Text = discount.ToString("F2");
        }

        private void TxtPrice_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                txtQuantity.Focus();
            }
        }

        private void TxtQuantity_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                AddItemToInvoice();
            }
        }

        private void TxtPayment_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                CalculateBalance();
            }
        }

        private void BtnAdd_Click(object sender, RoutedEventArgs e)
        {
            AddItemToInvoice();
        }

        private void AddItemToInvoice()
        {
            try
            {
                if (!ValidateItemInput())
                    return;

                var quantity = decimal.Parse(txtQuantity.Text);
                var unitPrice = decimal.Parse(txtPrice.Text);
                var discount = decimal.Parse(txtDiscount.Text);

                // Check stock availability
                if (_selectedItem != null && _selectedItem.ManageStock)
                {
                    var isReturn = cmbSaleType.SelectedItem is MetaData saleType && saleType.Value == "Return";
                    if (!isReturn && _selectedItem.Quantity < quantity)
                    {
                        MessageBox.Show("Insufficient stock!", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                // Calculate amounts
                var subTotal = quantity * unitPrice;
                var discountAmount = _isPercentageDiscount ? (subTotal * discount / 100) : (quantity * discount);
                var total = subTotal - discountAmount;

                // Create invoice record
                var record = new SalesInvoiceRecordViewModel
                {
                    Index = InvoiceItems.Count + 1,
                    ItemId = _selectedItem?.Id ?? 0,
                    ItemCode = _selectedItem?.ItemCode ?? "0",
                    ItemName = _selectedItem?.ItemName ?? txtItemName.Text,
                    UnitPrice = unitPrice,
                    Quantity = quantity,
                    SubTotal = subTotal,
                    Discount = discountAmount,
                    Price = total,
                    SaleTypeId = ((MetaData)cmbSaleType.SelectedItem).Id,
                    BusinessTypeId = ((MetaData)cmbBusinessType.SelectedItem).Id
                };

                InvoiceItems.Add(record);
                UpdateTotals();
                ClearItemFields();
                txtBarcode.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error adding item: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateItemInput()
        {
            if (string.IsNullOrEmpty(txtItemName.Text))
            {
                MessageBox.Show("Please select an item!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!decimal.TryParse(txtPrice.Text, out _) || decimal.Parse(txtPrice.Text) <= 0)
            {
                MessageBox.Show("Please enter a valid price!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!decimal.TryParse(txtQuantity.Text, out _) || decimal.Parse(txtQuantity.Text) <= 0)
            {
                MessageBox.Show("Please enter a valid quantity!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!decimal.TryParse(txtDiscount.Text, out _))
            {
                MessageBox.Show("Please enter a valid discount!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private void UpdateTotals()
        {
            var subTotal = InvoiceItems.Sum(i => i.SubTotal);
            var totalDiscount = InvoiceItems.Sum(i => i.Discount);
            var total = subTotal - totalDiscount;

            txtSubTotal.Text = subTotal.ToString("F2");
            txtTotalDiscount.Text = totalDiscount.ToString("F2");
            txtTotal.Text = total.ToString("F2");

            CalculateBalance();
        }

        private void CalculateBalance()
        {
            if (decimal.TryParse(txtTotal.Text, out var total) && 
                decimal.TryParse(txtPayment.Text, out var payment))
            {
                var balance = payment - total;
                txtBalance.Text = balance.ToString("F2");
            }
        }

        private void BtnRemove_Click(object sender, RoutedEventArgs e)
        {
            if (dgItems.SelectedItem is SalesInvoiceRecordViewModel selectedItem)
            {
                InvoiceItems.Remove(selectedItem);
                
                // Update indices
                for (int i = 0; i < InvoiceItems.Count; i++)
                {
                    InvoiceItems[i].Index = i + 1;
                }
                
                UpdateTotals();
            }
            else
            {
                MessageBox.Show("Please select an item to remove!", "Information", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            await SaveInvoice(false);
        }

        private async void BtnPrintAndSave_Click(object sender, RoutedEventArgs e)
        {
            await SaveInvoice(true);
        }

        private async Task SaveInvoice(bool printAfterSave)
        {
            try
            {
                if (!ValidateInvoice())
                    return;

                // Get or create customer
                var customer = await GetOrCreateCustomer();
                if (customer == null)
                    return;

                // Create sales invoice
                var salesInvoice = new SalesInvoice
                {
                    Date = dpDate.SelectedDate ?? DateTime.Now,
                    CustomerId = customer.Id,
                    PaymentMethodId = ((MetaData)cmbPaymentMethod.SelectedItem).Id,
                    SubTotal = decimal.Parse(txtSubTotal.Text),
                    TotalDiscount = decimal.Parse(txtTotalDiscount.Text),
                    TotalAmount = decimal.Parse(txtTotal.Text),
                    Payment = decimal.Parse(txtPayment.Text),
                    Balance = Math.Abs(decimal.Parse(txtBalance.Text)),
                    CashBalance = decimal.Parse(txtBalance.Text) > 0 ? decimal.Parse(txtBalance.Text) : 0,
                    Counter = "001", // This should come from configuration
                    SalesInvoiceRecords = InvoiceItems.Select(item => new SalesInvoiceRecord
                    {
                        ItemId = item.ItemId,
                        ItemCode = item.ItemCode,
                        Quantity = item.Quantity,
                        UnitPrice = item.UnitPrice,
                        SubTotal = item.SubTotal,
                        Discount = item.Discount,
                        Price = item.Price,
                        SaleTypeId = item.SaleTypeId,
                        BusinessTypeId = item.BusinessTypeId,
                        Date = DateTime.Now,
                        Counter = "001"
                    }).ToList()
                };

                var invoiceNumber = await _salesInvoiceService.CreateSalesInvoiceAsync(salesInvoice);
                
                if (invoiceNumber != null)
                {
                    MessageBox.Show($"Sales invoice saved successfully! Invoice No: {invoiceNumber}", 
                        "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    if (printAfterSave)
                    {
                        // TODO: Implement printing functionality
                        MessageBox.Show("Printing functionality will be implemented here.", 
                            "Print", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    
                    ClearForm();
                }
                else
                {
                    MessageBox.Show("Failed to save sales invoice!", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving invoice: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInvoice()
        {
            if (InvoiceItems.Count == 0)
            {
                MessageBox.Show("Please add at least one item!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!decimal.TryParse(txtPayment.Text, out _))
            {
                MessageBox.Show("Please enter a valid payment amount!", "Validation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private async Task<Customer?> GetOrCreateCustomer()
        {
            try
            {
                var customerName = txtCustomer.Text.Trim();
                
                if (string.IsNullOrEmpty(customerName))
                {
                    // Use default customer
                    return await _customerService.GetDefaultCustomerAsync();
                }

                // Search for existing customer
                var customers = await _customerService.SearchCustomersByNameAsync(customerName);
                var existingCustomer = customers.FirstOrDefault(c => 
                    c.Name.Equals(customerName, StringComparison.OrdinalIgnoreCase));

                if (existingCustomer != null)
                {
                    return existingCustomer;
                }

                // Create new customer
                var newCustomer = new Customer
                {
                    CustomerCode = $"CUST{DateTime.Now:yyyyMMddHHmmss}",
                    Name = customerName
                };

                var created = await _customerService.CreateCustomerAsync(newCustomer);
                return created ? newCustomer : null;
            }
            catch
            {
                return await _customerService.GetDefaultCustomerAsync();
            }
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("Are you sure you want to clear all data?", "Confirm Clear", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                ClearForm();
            }
        }

        private void ClearForm()
        {
            InvoiceItems.Clear();
            ClearItemFields();
            txtCustomer.Clear();
            txtSubTotal.Clear();
            txtTotalDiscount.Clear();
            txtTotal.Clear();
            txtPayment.Clear();
            txtBalance.Clear();
            dpDate.SelectedDate = DateTime.Today;
            
            // Reset combo boxes to default
            if (cmbBusinessType.Items.Count > 0) cmbBusinessType.SelectedIndex = 0;
            if (cmbSaleType.Items.Count > 0) cmbSaleType.SelectedIndex = 0;
            if (cmbPaymentMethod.Items.Count > 0) cmbPaymentMethod.SelectedIndex = 0;
            
            txtBarcode.Focus();
        }

        private void ClearItemFields()
        {
            txtBarcode.Clear();
            txtItemName.Clear();
            txtPrice.Clear();
            txtQuantity.Clear();
            txtDiscount.Text = "0";
            _selectedItem = null;
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // ViewModel for displaying invoice items in the DataGrid
    public class SalesInvoiceRecordViewModel
    {
        public int Index { get; set; }
        public int ItemId { get; set; }
        public string ItemCode { get; set; } = string.Empty;
        public string ItemName { get; set; } = string.Empty;
        public decimal UnitPrice { get; set; }
        public decimal Quantity { get; set; }
        public decimal SubTotal { get; set; }
        public decimal Discount { get; set; }
        public decimal Price { get; set; }
        public int SaleTypeId { get; set; }
        public int BusinessTypeId { get; set; }
    }
}
