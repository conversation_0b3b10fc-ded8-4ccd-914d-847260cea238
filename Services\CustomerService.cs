using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using GroceryERP.Data;
using GroceryERP.Models;

namespace GroceryERP.Services
{
    public class CustomerService : ICustomerService
    {
        private readonly GroceryERPContext _context;

        public CustomerService(GroceryERPContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Customer>> GetAllCustomersAsync()
        {
            return await _context.Customers
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<Customer?> GetCustomerByIdAsync(int id)
        {
            return await _context.Customers
                .FirstOrDefaultAsync(c => c.Id == id && c.IsActive);
        }

        public async Task<Customer?> GetCustomerByCodeAsync(string customerCode)
        {
            return await _context.Customers
                .FirstOrDefaultAsync(c => c.CustomerCode == customerCode && c.IsActive);
        }

        public async Task<Customer?> GetDefaultCustomerAsync()
        {
            return await _context.Customers
                .FirstOrDefaultAsync(c => c.CustomerCode == "DEFAULT" && c.IsActive);
        }

        public async Task<IEnumerable<Customer>> SearchCustomersByNameAsync(string name)
        {
            return await _context.Customers
                .Where(c => c.IsActive && c.Name.Contains(name))
                .OrderBy(c => c.Name)
                .Take(50)
                .ToListAsync();
        }

        public async Task<bool> CreateCustomerAsync(Customer customer)
        {
            try
            {
                // Check if customer code already exists
                var existingCustomer = await _context.Customers
                    .FirstOrDefaultAsync(c => c.CustomerCode == customer.CustomerCode);

                if (existingCustomer != null)
                    return false;

                _context.Customers.Add(customer);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateCustomerAsync(Customer customer)
        {
            try
            {
                // Check if another customer has the same customer code
                var existingCustomer = await _context.Customers
                    .FirstOrDefaultAsync(c => c.Id != customer.Id && c.CustomerCode == customer.CustomerCode);

                if (existingCustomer != null)
                    return false;

                _context.Customers.Update(customer);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteCustomerAsync(int id)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(id);
                if (customer == null)
                    return false;

                // Check if customer has any sales invoices
                var hasSalesInvoices = await _context.SalesInvoices
                    .AnyAsync(si => si.CustomerId == id);

                if (hasSalesInvoices)
                {
                    // Soft delete if has transactions
                    customer.IsActive = false;
                }
                else
                {
                    // Hard delete if no transactions
                    _context.Customers.Remove(customer);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
